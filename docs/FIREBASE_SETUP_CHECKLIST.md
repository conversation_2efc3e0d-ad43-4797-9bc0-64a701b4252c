# Firebase Setup Checklist

## 🔥 Firebase Project Setup

### 1. Create Firebase Project
- [ ] Go to [Firebase Console](https://console.firebase.google.com)
- [ ] Click "Create a project" or "Add project"
- [ ] Enter project name (e.g., "YellowTaxi")
- [ ] Complete project creation wizard

### 2. Enable Services
- [ ] **Authentication**: Go to Authentication → Sign-in method
  - [ ] Enable Email/Password
  - [ ] Enable Google (optional)
  - [ ] Enable Facebook (optional)
  - [ ] Enable Phone (requires phone verification)
- [ ] **Firestore**: Go to Firestore Database → Create database
  - [ ] Choose "Start in test mode" for now
  - [ ] Select a location (closest to your users)

### 3. Get Configuration
- [ ] Go to Project Settings (gear icon)
- [ ] Scroll down to "Your apps" section
- [ ] Click "Add app" → Web app (</>) icon
- [ ] Register app with nickname (e.g., "YellowTaxi Web")
- [ ] Copy the configuration object

## 🔧 Environment Setup

### 4. Add Environment Variables
Create `.env.local` file in your project root:

```env
# Required Firebase Configuration
NEXT_PUBLIC_FIREBASE_API_KEY=your_api_key_here
NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
NEXT_PUBLIC_FIREBASE_PROJECT_ID=your_project_id
NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
NEXT_PUBLIC_FIREBASE_APP_ID=your_app_id
NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

### 5. Verify Setup
- [ ] Restart your development server: `npm run dev`
- [ ] Check browser console for Firebase configuration logs
- [ ] Use the Firebase Debug component on the login page
- [ ] Test Firebase connection

## 🛠️ Common Issues & Solutions

### "Failed to get document because the client is offline"
- **Cause**: Firebase project doesn't exist or Firestore isn't enabled
- **Solution**: 
  1. Check Firebase Console - make sure project exists
  2. Enable Firestore Database in Firebase Console
  3. Verify environment variables are correct

### "Permission denied" errors
- **Cause**: Firestore security rules are too restrictive
- **Solution**: 
  1. Go to Firestore → Rules
  2. Temporarily use test rules: `allow read, write: if true;`
  3. Deploy the provided `firestore.rules` file later

### Environment variables not working
- **Cause**: Wrong file name or server not restarted
- **Solution**:
  1. Make sure file is named `.env.local` (not `.env`)
  2. Restart development server
  3. Check variables start with `NEXT_PUBLIC_`

### Firebase project ID mismatch
- **Cause**: Wrong project ID in environment variables
- **Solution**:
  1. Double-check project ID in Firebase Console
  2. Verify it matches `NEXT_PUBLIC_FIREBASE_PROJECT_ID`

## 🧪 Testing Your Setup

1. **Configuration Test**: Check browser console for Firebase config logs
2. **Connection Test**: Use the Firebase Debug component
3. **Authentication Test**: Try to register a new user
4. **Firestore Test**: Check if user documents are created

## 📞 Need Help?

If you're still having issues:

1. Check the browser console for detailed error messages
2. Verify all checklist items above
3. Try creating a fresh Firebase project
4. Check Firebase Console for any service issues

## 🚀 Next Steps After Setup

Once Firebase is working:
1. Deploy Firestore security rules from `firestore.rules`
2. Set up proper authentication flows
3. Configure social login providers
4. Test in production environment
