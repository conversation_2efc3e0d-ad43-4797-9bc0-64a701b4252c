# 🗺️ Google Maps API Setup Guide

This guide will help you set up Google Maps API integration for the Yellow Taxi ride-ordering system.

## 📋 Prerequisites

- Google Cloud Platform account
- Billing enabled (required for Maps APIs)
- Basic understanding of environment variables

## 🚀 Step-by-Step Setup

### 1. Create Google Cloud Project

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Click **Select a project** → **New Project**
3. Enter project name: `yellow-taxi-maps` (or your preferred name)
4. Click **Create**
5. Select your new project from the dropdown

### 2. Enable Billing

1. Go to **Billing** in the left sidebar
2. Link a billing account (required for Maps APIs)
3. Verify billing is enabled for your project

### 3. Enable Required APIs

Navigate to **APIs & Services** → **Library** and enable these APIs:

#### Required APIs:
- **Maps JavaScript API** - For map display and interaction
- **Places API** - For location search and autocomplete
- **Directions API** - For route calculation and navigation
- **Geocoding API** - For address to coordinates conversion

#### Optional APIs (for advanced features):
- **Distance Matrix API** - For travel time calculations
- **Roads API** - For road snapping and speed limits
- **Geolocation API** - For device location detection

### 4. Create API Key

1. Go to **APIs & Services** → **Credentials**
2. Click **+ CREATE CREDENTIALS** → **API key**
3. Copy the generated API key (starts with `AIza...`)
4. Click **RESTRICT KEY** (recommended for security)

### 5. Secure Your API Key

#### Application Restrictions:
1. Select **HTTP referrers (web sites)**
2. Add these referrers:
   ```
   localhost:3000/*
   localhost:3001/*
   *.vercel.app/*
   yourdomain.com/*
   *.yourdomain.com/*
   ```

#### API Restrictions:
1. Select **Restrict key**
2. Choose these APIs:
   - Maps JavaScript API
   - Places API
   - Directions API
   - Geocoding API

### 6. Add to Your Project

Create or update your `.env.local` file in the project root:

```env
# Google Maps API Configuration
NEXT_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx

# Optional: Customize map settings
NEXT_PUBLIC_GOOGLE_MAPS_REGION=JO
NEXT_PUBLIC_GOOGLE_MAPS_LANGUAGE=en
```

**Important:** 
- Replace `AIzaSyBxxxxxxxxxxxxxxxxxxxxxxxxxxxxxxx` with your actual API key
- Never commit your API key to version control
- Add `.env.local` to your `.gitignore` file

## 🔧 Configuration Options

### Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| `NEXT_PUBLIC_GOOGLE_MAPS_API_KEY` | Your Google Maps API key | - | Yes |
| `NEXT_PUBLIC_GOOGLE_MAPS_REGION` | Region code for localization | `JO` | No |
| `NEXT_PUBLIC_GOOGLE_MAPS_LANGUAGE` | Language code | `en` | No |

### Map Features Enabled

With a valid API key, you get:

✅ **Real Google Maps** - Interactive map with satellite/terrain views
✅ **Location Autocomplete** - Smart address suggestions
✅ **Route Visualization** - Turn-by-turn directions
✅ **Real-time Tracking** - Accurate GPS positioning
✅ **Distance Calculation** - Precise fare calculations
✅ **Place Details** - Rich location information

## 🧪 Testing Your Setup

### 1. Check API Key Status

Visit your customer dashboard at `/customer` and look for the map status indicator:

- 🗺️ **"Google Maps Active"** - API key working correctly
- ⚠️ **"Maps API Error - Demo Mode"** - API key invalid or quota exceeded
- ⏳ **"Loading Google Maps..."** - API loading in progress
- 📍 **"Demo Mode - Add API key for real maps"** - No API key configured

### 2. Test Location Search

1. Try booking a ride
2. Start typing in the pickup location field
3. You should see autocomplete suggestions from Google Places

### 3. Verify Map Features

- **Markers**: Pickup (green), destination (red), driver (yellow)
- **Routes**: Blue line showing driving directions
- **Interactions**: Zoom, pan, satellite view toggle

## 💰 Pricing Information

Google Maps APIs use a pay-as-you-go model:

### Free Tier (Monthly):
- **Maps JavaScript API**: $200 credit (~28,500 map loads)
- **Places API**: $200 credit (~17,000 requests)
- **Directions API**: $200 credit (~40,000 requests)
- **Geocoding API**: $200 credit (~40,000 requests)

### Typical Usage for Ride App:
- **Small app** (100 rides/day): ~$10-20/month
- **Medium app** (1000 rides/day): ~$100-200/month
- **Large app** (10,000 rides/day): ~$1000-2000/month

## 🔒 Security Best Practices

### 1. API Key Restrictions
- Always restrict your API key to specific domains
- Use separate keys for development and production
- Regularly rotate your API keys

### 2. Usage Monitoring
- Set up billing alerts in Google Cloud Console
- Monitor API usage in the console
- Set daily quotas to prevent unexpected charges

### 3. Error Handling
- The app gracefully falls back to demo mode if API fails
- Users can still book rides without Google Maps
- Error logging helps identify issues

## 🚨 Troubleshooting

### Common Issues:

#### "This page can't load Google Maps correctly"
- Check if billing is enabled
- Verify API key is correct
- Ensure required APIs are enabled

#### "Google Maps API error: RefererNotAllowedMapError"
- Add your domain to HTTP referrers
- Check for typos in referrer URLs
- Ensure localhost is included for development

#### "You have exceeded your daily request quota"
- Check usage in Google Cloud Console
- Increase quotas or upgrade billing plan
- Optimize API calls to reduce usage

#### Map shows but no autocomplete
- Enable Places API
- Check API restrictions include Places API
- Verify network connectivity

## 📞 Support

If you need help with Google Maps setup:

1. **Google Cloud Support**: [cloud.google.com/support](https://cloud.google.com/support)
2. **Maps API Documentation**: [developers.google.com/maps](https://developers.google.com/maps)
3. **Stack Overflow**: Tag questions with `google-maps-api`

## 🎯 Next Steps

After setting up Google Maps:

1. **Test thoroughly** with real addresses in Jordan
2. **Monitor usage** and costs in Google Cloud Console
3. **Optimize performance** by caching common locations
4. **Add advanced features** like traffic data and ETA calculations
5. **Set up monitoring** and alerts for API errors

---

**Note**: The app works perfectly in demo mode without Google Maps API. Adding the API key enhances the experience with real maps and location services, but it's not required for basic functionality.
