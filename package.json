{"name": "my-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "seed:admin": "tsx scripts/seed-admin-user.ts", "seed:phone-admin": "tsx scripts/seed-phone-admin-user.ts", "seed:customer": "tsx scripts/seed-customer-user.ts", "seed:driver": "tsx scripts/seed-driver-user.ts", "seed:phone-customer": "tsx scripts/seed-phone-customer-user.ts", "seed:phone-driver": "tsx scripts/seed-phone-driver-user.ts", "seed:all": "tsx scripts/seed-admin-user.ts && tsx scripts/seed-phone-admin-user.ts && tsx scripts/seed-customer-user.ts && tsx scripts/seed-driver-user.ts && tsx scripts/seed-phone-customer-user.ts && tsx scripts/seed-phone-driver-user.ts", "fix:driver": "tsx scripts/fix-driver-document.ts"}, "dependencies": {"@google-cloud/firestore": "^7.11.3", "@google-cloud/functions-framework": "^4.0.0", "@google/maps": "^1.1.3", "@hookform/resolvers": "^5.2.1", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.3", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.16", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-select": "^2.2.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.6", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-toast": "^1.2.15", "chart.js": "^4.5.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "firebase": "^12.0.0", "lucide-react": "^0.537.0", "next": "15.4.6", "react": "19.1.0", "react-chartjs-2": "^5.3.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "react-hot-toast": "^2.6.0", "sonner": "^2.0.7", "tailwind-merge": "^3.3.1", "zod": "^4.0.15", "zustand": "^5.0.7"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^17.2.1", "eslint": "^9", "eslint-config-next": "15.4.6", "firebase-admin": "^13.4.0", "tailwindcss": "^4", "tsx": "^4.20.4", "tw-animate-css": "^1.3.6", "typescript": "^5"}}