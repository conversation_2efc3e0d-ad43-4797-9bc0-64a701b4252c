#!/usr/bin/env tsx

/**
 * Fix Driver Document Script
 * 
 * This script creates a proper driver document in the drivers collection
 * for <NAME_EMAIL> user account
 * 
 * Usage:
 * 1. Set environment variables (see .env.example)
 * 2. Run: npx tsx scripts/fix-driver-document.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp, GeoPoint } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const DRIVER_EMAIL = '<EMAIL>';

interface DriverDocument {
  id: string;
  personalInfo: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    nationalId: string;
    licenseNumber: string;
    licenseExpiry: string;
    emergencyContact: {
      name: string;
      phone: string;
      relationship: string;
    };
  };
  vehicle: {
    make: string;
    model: string;
    year: number;
    color: string;
    plateNumber: string;
    licenseNumber: string;
    licenseExpiry: string;
    insurance: {
      provider: string;
      policyNumber: string;
      expiryDate: string;
    };
  };
  documents: {
    nationalIdFront: string;
    nationalIdBack: string;
    drivingLicenseFront: string;
    drivingLicenseBack: string;
    vehicleLicense: string;
    vehicleImages: string[];
    insuranceCertificate: string;
    backgroundCheck: string;
  };
  verification: {
    status: 'pending' | 'approved' | 'rejected';
    verifiedAt?: Timestamp;
    verifiedBy?: string;
    notes?: string;
  };
  location: {
    current: GeoPoint;
    heading: number;
    speed: number;
    accuracy: number;
    lastUpdated: Timestamp;
  };
  status: {
    online: boolean;
    available: boolean;
    currentOrder?: string;
    lastSeen: Timestamp;
  };
  earnings: {
    totalEarnings: number;
    pendingPayout: number;
    completedTrips: number;
    rating: {
      average: number;
      count: number;
      breakdown: {
        5: number;
        4: number;
        3: number;
        2: number;
        1: number;
      };
    };
  };
  schedule: {
    workingHours: {
      monday: { start: string; end: string; active: boolean };
      tuesday: { start: string; end: string; active: boolean };
      wednesday: { start: string; end: string; active: boolean };
      thursday: { start: string; end: string; active: boolean };
      friday: { start: string; end: string; active: boolean };
      saturday: { start: string; end: string; active: boolean };
      sunday: { start: string; end: string; active: boolean };
    };
    maxDistance: number;
    preferredAreas: string[];
  };
  metadata: {
    createdAt: Timestamp;
    updatedAt: Timestamp;
    version: string;
  };
}

class DriverDocumentFixer {
  private auth!: Auth;
  private db!: Firestore;
  private isInitialized = false;

  constructor() {
    this.initializeFirebaseAdmin();
  }

  private initializeFirebaseAdmin(): void {
    try {
      if (this.isInitialized) {
        return;
      }

      const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
      
      if (!serviceAccountPath) {
        throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH environment variable is required');
      }

      const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));

      initializeApp({
        credential: cert(serviceAccount),
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      });

      this.auth = getAuth();
      this.db = getFirestore();
      this.isInitialized = true;

      console.log('✅ Firebase Admin initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  async fixDriverDocument(): Promise<void> {
    try {
      console.log('🚀 Starting driver document fix...');
      
      // Get the user by email
      const userRecord = await this.auth.getUserByEmail(DRIVER_EMAIL);
      console.log(`📧 Found user: ${userRecord.uid}`);

      // Check if user document exists
      const userDoc = await this.db.collection('users').doc(userRecord.uid).get();
      if (!userDoc.exists) {
        throw new Error('User document not found');
      }

      const userData = userDoc.data();
      console.log(`👤 User roles: ${userData?.roles?.join(', ')}`);

      // Check if driver document already exists
      const driverDoc = await this.db.collection('drivers').doc(userRecord.uid).get();
      
      if (driverDoc.exists) {
        console.log('⚠️  Driver document already exists, updating...');
      } else {
        console.log('🆕 Creating new driver document...');
      }

      // Create the driver document
      await this.createDriverDocument(userRecord.uid, userData);
      
      console.log('✅ Driver document created/updated successfully!');
      
    } catch (error) {
      console.error('❌ Failed to fix driver document:', error);
      throw error;
    }
  }

  private async createDriverDocument(uid: string, userData: any): Promise<void> {
    const now = Timestamp.now();
    
    const driverDocument: DriverDocument = {
      id: uid,
      personalInfo: {
        firstName: userData?.profile?.firstName || 'Ahmed',
        lastName: userData?.profile?.lastName || 'Driver',
        phone: userData?.profile?.phone || '+************',
        email: userData?.profile?.email || DRIVER_EMAIL,
        nationalId: '*********0',
        licenseNumber: 'JO-*********',
        licenseExpiry: '2029-12-31',
        emergencyContact: {
          name: 'Emergency Contact',
          phone: '+************',
          relationship: 'Family'
        }
      },
      vehicle: {
        make: 'Toyota',
        model: 'Camry',
        year: 2020,
        color: 'White',
        plateNumber: 'JO-1234-A',
        licenseNumber: 'VL-123456',
        licenseExpiry: '2029-12-31',
        insurance: {
          provider: 'Jordan Insurance Company',
          policyNumber: 'INS-123456',
          expiryDate: '2025-12-31'
        }
      },
      documents: {
        nationalIdFront: 'https://example.com/national-id-front.jpg',
        nationalIdBack: 'https://example.com/national-id-back.jpg',
        drivingLicenseFront: 'https://example.com/license-front.jpg',
        drivingLicenseBack: 'https://example.com/license-back.jpg',
        vehicleLicense: 'https://example.com/vehicle-license.jpg',
        vehicleImages: [
          'https://example.com/vehicle-front.jpg',
          'https://example.com/vehicle-back.jpg'
        ],
        insuranceCertificate: 'https://example.com/insurance.pdf',
        backgroundCheck: 'https://example.com/background-check.pdf'
      },
      verification: {
        status: 'approved',
        verifiedAt: now,
        verifiedBy: 'system',
        notes: 'Demo account - auto-approved'
      },
      location: {
        current: new GeoPoint(31.9539, 35.9106), // Amman coordinates
        heading: 0,
        speed: 0,
        accuracy: 10,
        lastUpdated: now
      },
      status: {
        online: false,
        available: false,
        lastSeen: now
      },
      earnings: {
        totalEarnings: 0,
        pendingPayout: 0,
        completedTrips: 0,
        rating: {
          average: 5.0,
          count: 0,
          breakdown: {
            5: 0,
            4: 0,
            3: 0,
            2: 0,
            1: 0
          }
        }
      },
      schedule: {
        workingHours: {
          monday: { start: '08:00', end: '20:00', active: true },
          tuesday: { start: '08:00', end: '20:00', active: true },
          wednesday: { start: '08:00', end: '20:00', active: true },
          thursday: { start: '08:00', end: '20:00', active: true },
          friday: { start: '08:00', end: '20:00', active: true },
          saturday: { start: '08:00', end: '20:00', active: true },
          sunday: { start: '08:00', end: '20:00', active: false }
        },
        maxDistance: 50,
        preferredAreas: ['Amman', 'Zarqa']
      },
      metadata: {
        createdAt: now,
        updatedAt: now,
        version: '1.0'
      }
    };

    // Set the driver document
    await this.db.collection('drivers').doc(uid).set(driverDocument, { merge: true });
    console.log('✅ Driver document created in drivers collection');
  }

  async verifyFix(): Promise<void> {
    try {
      console.log('🔍 Verifying the fix...');
      
      const userRecord = await this.auth.getUserByEmail(DRIVER_EMAIL);
      
      // Check user document
      const userDoc = await this.db.collection('users').doc(userRecord.uid).get();
      if (userDoc.exists) {
        const userData = userDoc.data();
        console.log(`✅ User document exists with roles: ${userData?.roles?.join(', ')}`);
      }
      
      // Check driver document
      const driverDoc = await this.db.collection('drivers').doc(userRecord.uid).get();
      if (driverDoc.exists) {
        const driverData = driverDoc.data();
        console.log('✅ Driver document exists');
        console.log(`   Status: ${driverData?.verification?.status}`);
        console.log(`   Vehicle: ${driverData?.vehicle?.make} ${driverData?.vehicle?.model}`);
        console.log(`   Online: ${driverData?.status?.online}`);
      } else {
        console.log('❌ Driver document not found');
      }
      
    } catch (error) {
      console.error('❌ Verification failed:', error);
      throw error;
    }
  }
}

// Main execution
async function main(): Promise<void> {
  try {
    console.log('🚕 YellowTaxi Driver Document Fix');
    console.log('================================\n');

    const requiredEnvVars = [
      'FIREBASE_SERVICE_ACCOUNT_PATH',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    }

    const fixer = new DriverDocumentFixer();
    
    await fixer.fixDriverDocument();
    await fixer.verifyFix();
    
    console.log('\n🎉 Driver document fix completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Login to the driver dashboard');
    console.log('   2. Try to go online');
    console.log('   3. Verify the toggle works correctly');

  } catch (error) {
    console.error('\n❌ Fix failed:', error);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { DriverDocumentFixer, main };
