#!/usr/bin/env tsx

/**
 * Phone Admin User Seeding Script
 * 
 * This script creates an admin user account with phone provider +***********
 * and specific ID e7o2R6UtvZU1ylEVvS3ymnv6Icw1
 * 
 * Usage:
 * 1. Set environment variables (see .env.local)
 * 2. Run: npx tsx scripts/seed-phone-admin-user.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import { join } from 'path';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

// Admin user configuration
const ADMIN_USER_CONFIG = {
  uid: 'e7o2R6UtvZU1ylEVvS3ymnv6Icw1',
  phone: '+***********',
  firstName: 'Phone',
  lastName: 'Admin',
  roles: ['admin'],
  profile: {
    firstName: 'Phone',
    lastName: 'Admin',
    phone: '+***********',
    email: '',
    avatar: '',
    gender: 'male' as const,
    language: 'en' as const,
  }
};

interface AdminUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    avatar: string;
    gender: 'male' | 'female';
    language: 'en' | 'ar';
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
  roles: string[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: string[];
    lastLogin: Timestamp;
    activeUntil: Timestamp;
  };
  settings: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      system: boolean;
    };
    privacy: {
      shareLocation: boolean;
      showProfile: boolean;
    };
    theme: 'light' | 'dark';
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageRating: number;
    joinedAt: Timestamp;
  };
  status: 'active' | 'inactive' | 'suspended';
}

class PhoneAdminUserSeeder {
  private auth!: Auth;
  private db!: Firestore;
  private isInitialized = false;

  constructor() {
    this.initializeFirebaseAdmin();
  }

  private initializeFirebaseAdmin(): void {
    try {
      // Check if Firebase Admin is already initialized
      if (this.isInitialized) {
        return;
      }

      // Get service account key path
      const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
      
      if (!serviceAccountPath) {
        throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH environment variable is required');
      }

      // Read service account key
      const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));

      // Initialize Firebase Admin
      initializeApp({
        credential: cert(serviceAccount),
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
        databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
      });

      this.auth = getAuth();
      this.db = getFirestore();
      this.isInitialized = true;

      console.log('✅ Firebase Admin initialized successfully');
    } catch (error) {
      console.error('❌ Failed to initialize Firebase Admin:', error);
      throw error;
    }
  }

  async seedPhoneAdminUser(): Promise<void> {
    try {
      console.log('🚀 Starting phone admin user seeding process...');
      console.log(`📱 Phone: ${ADMIN_USER_CONFIG.phone}`);
      console.log(`🆔 UID: ${ADMIN_USER_CONFIG.uid}`);

      // Check if user already exists
      const existingUser = await this.checkExistingUser();
      
      if (existingUser) {
        console.log('⚠️  Phone admin user already exists, updating roles and profile...');
        await this.updateExistingUser(existingUser.uid);
      } else {
        console.log('🆕 Creating new phone admin user...');
        await this.createNewPhoneAdminUser();
      }

      console.log('✅ Phone admin user seeding completed successfully!');
      console.log('🔑 Login credentials:');
      console.log(`   Phone: ${ADMIN_USER_CONFIG.phone}`);
      console.log(`   UID: ${ADMIN_USER_CONFIG.uid}`);
      console.log('⚠️  This user can be used for testing phone authentication!');

    } catch (error) {
      console.error('❌ Phone admin user seeding failed:', error);
      throw error;
    }
  }

  private async checkExistingUser(): Promise<import('firebase-admin/auth').UserRecord | null> {
    try {
      // Try to get user by UID
      const userRecord = await this.auth.getUser(ADMIN_USER_CONFIG.uid);
      return userRecord;
    } catch (error: unknown) {
      if (error && typeof error === 'object' && 'code' in error && error.code === 'auth/user-not-found') {
        return null;
      }
      throw error;
    }
  }

  private async createNewPhoneAdminUser(): Promise<void> {
    try {
      // Create user in Firebase Auth with specific UID
      console.log('🔐 Creating Firebase Auth user with specific UID...');
      const userRecord = await this.auth.createUser({
        uid: ADMIN_USER_CONFIG.uid,
        phoneNumber: ADMIN_USER_CONFIG.phone,
        displayName: `${ADMIN_USER_CONFIG.firstName} ${ADMIN_USER_CONFIG.lastName}`,
        emailVerified: false,
        disabled: false,
      });

      console.log(`✅ Firebase Auth user created with UID: ${userRecord.uid}`);

      // Create user document in Firestore
      console.log('📄 Creating Firestore user document...');
      await this.createUserDocument(userRecord.uid);

      // Set custom claims for admin role
      console.log('🔑 Setting admin custom claims...');
      await this.auth.setCustomUserClaims(userRecord.uid, {
        roles: ADMIN_USER_CONFIG.roles,
        isAdmin: true,
        permissions: ['*'] // All permissions for admin
      });

      console.log('✅ Phone admin user creation completed successfully');

    } catch (error) {
      console.error('❌ Failed to create phone admin user:', error);
      throw error;
    }
  }

  private async updateExistingUser(uid: string): Promise<void> {
    try {
      // Update user profile in Firebase Auth
      console.log('🔄 Updating existing user profile...');
      await this.auth.updateUser(uid, {
        displayName: `${ADMIN_USER_CONFIG.firstName} ${ADMIN_USER_CONFIG.lastName}`,
        phoneNumber: ADMIN_USER_CONFIG.phone,
      });

      // Update or create user document in Firestore
      console.log('📄 Updating Firestore user document...');
      await this.createUserDocument(uid);

      // Update custom claims
      console.log('🔑 Updating admin custom claims...');
      await this.auth.setCustomUserClaims(uid, {
        roles: ADMIN_USER_CONFIG.roles,
        isAdmin: true,
        permissions: ['*']
      });

      console.log('✅ Existing user updated successfully');

    } catch (error) {
      console.error('❌ Failed to update existing user:', error);
      throw error;
    }
  }

  private async createUserDocument(uid: string): Promise<void> {
    try {
      const now = Timestamp.now();
      const userData: AdminUserData = {
        profile: {
          ...ADMIN_USER_CONFIG.profile,
          createdAt: now,
          updatedAt: now,
        },
        roles: ADMIN_USER_CONFIG.roles,
        authentication: {
          phoneVerified: true,
          emailVerified: false,
          providers: ['phone'],
          lastLogin: now,
          activeUntil: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)), // 1 year
        },
        settings: {
          notifications: {
            orderUpdates: true,
            promotions: true,
            system: true,
          },
          privacy: {
            shareLocation: true,
            showProfile: true,
          },
          theme: 'light',
        },
        stats: {
          totalOrders: 0,
          totalSpent: 0,
          averageRating: 5,
          joinedAt: now,
        },
        status: 'active',
      };

      // Set document with merge to avoid overwriting existing data
      await this.db.collection('users').doc(uid).set(userData, { merge: true });
      console.log('✅ Firestore user document created/updated successfully');

    } catch (error) {
      console.error('❌ Failed to create/update Firestore document:', error);
      throw error;
    }
  }

  async verifyPhoneAdminUser(): Promise<void> {
    try {
      console.log('🔍 Verifying phone admin user setup...');
      
      const userRecord = await this.auth.getUser(ADMIN_USER_CONFIG.uid);
      console.log(`✅ Firebase Auth user verified: ${userRecord.uid}`);
      console.log(`   Phone: ${userRecord.phoneNumber}`);
      console.log(`   Display Name: ${userRecord.displayName}`);

      const userDoc = await this.db.collection('users').doc(userRecord.uid).get();
      if (userDoc.exists) {
        console.log('✅ Firestore user document verified');
        const userData = userDoc.data();
        console.log(`   Roles: ${userData?.roles?.join(', ')}`);
        console.log(`   Status: ${userData?.status}`);
        console.log(`   Phone Verified: ${userData?.authentication?.phoneVerified}`);
        console.log(`   Providers: ${userData?.authentication?.providers?.join(', ')}`);
      } else {
        console.log('❌ Firestore user document not found');
      }

      const customClaims = userRecord.customClaims;
      if (customClaims?.isAdmin) {
        console.log('✅ Admin custom claims verified');
        console.log(`   Permissions: ${customClaims.permissions?.join(', ')}`);
      } else {
        console.log('❌ Admin custom claims not found');
      }

    } catch (error) {
      console.error('❌ Phone admin user verification failed:', error);
      throw error;
    }
  }
}

// Main execution function
async function main(): Promise<void> {
  try {
    console.log('🚕 YellowTaxi Phone Admin User Seeding Script');
    console.log('============================================\n');

    // Validate environment variables
    const requiredEnvVars = [
      'FIREBASE_SERVICE_ACCOUNT_PATH',
      'NEXT_PUBLIC_FIREBASE_PROJECT_ID'
    ];

    const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
    if (missingEnvVars.length > 0) {
      throw new Error(`Missing required environment variables: ${missingEnvVars.join(', ')}`);
    }

    const seeder = new PhoneAdminUserSeeder();
    
    // Seed the phone admin user
    await seeder.seedPhoneAdminUser();
    
    console.log('\n🔍 Verifying setup...');
    await seeder.verifyPhoneAdminUser();
    
    console.log('\n🎉 Phone admin user seeding completed successfully!');
    console.log('\n📋 Next steps:');
    console.log('   1. Test phone authentication with +***********');
    console.log('   2. Verify admin access in the dashboard');
    console.log('   3. Test OTP functionality with this test number');
    console.log('   4. Configure additional admin permissions if needed');

  } catch (error) {
    console.error('\n❌ Seeding failed:', error);
    process.exit(1);
  }
}

// Run the script if called directly
if (require.main === module) {
  main().catch(console.error);
}

export { PhoneAdminUserSeeder, main };
