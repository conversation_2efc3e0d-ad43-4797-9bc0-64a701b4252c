#!/usr/bin/env tsx

/**
 * Phone Customer User Seeding Script
 *
 * Creates/updates a customer user authenticated by phone, for testing.
 * Phone: +13333333333
 *
 * Usage:
 *  - npx tsx scripts/seed-phone-customer-user.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const CUSTOMER_USER_CONFIG = {
  phone: '+13333333333',
  firstName: 'Phone',
  lastName: 'Customer',
  roles: ['customer'],
  profile: {
    firstName: 'Phone',
    lastName: 'Customer',
    phone: '+13333333333',
    email: '',
    avatar: '',
    gender: 'male' as const,
    language: 'en' as const,
  }
};

interface CustomerUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    avatar: string;
    gender: 'male' | 'female';
    language: 'en' | 'ar';
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
  roles: string[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: string[];
    lastLogin: Timestamp;
    activeUntil: Timestamp;
  };
  settings: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      system: boolean;
    };
    privacy: {
      shareLocation: boolean;
      showProfile: boolean;
    };
    theme: 'light' | 'dark';
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageRating: number;
    joinedAt: Timestamp;
  };
  status: 'active' | 'inactive' | 'suspended';
}

class PhoneCustomerUserSeeder {
  private auth!: Auth;
  private db!: Firestore;
  private initialized = false;

  constructor() {
    this.init();
  }

  private init(): void {
    if (this.initialized) return;

    const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
    if (!serviceAccountPath) {
      throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH is required');
    }

    const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));
    initializeApp({
      credential: cert(serviceAccount),
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
    });
    this.auth = getAuth();
    this.db = getFirestore();
    this.initialized = true;
    console.log('✅ Firebase Admin initialized');
  }

  async seed(): Promise<void> {
    console.log('🚀 Seeding phone customer user...');
    console.log(`📱 Phone: ${CUSTOMER_USER_CONFIG.phone}`);

    const existing = await this.findByPhone(CUSTOMER_USER_CONFIG.phone);
    if (existing) {
      console.log('⚠️  Customer exists, updating...');
      await this.update(existing.uid);
    } else {
      console.log('🆕 Creating customer...');
      await this.create();
    }
  }

  private async findByPhone(phone: string) {
    try {
      return await this.auth.getUserByPhoneNumber(phone);
    } catch (e: any) {
      if (e?.code === 'auth/user-not-found') return null;
      throw e;
    }
  }

  private async create(): Promise<void> {
    const user = await this.auth.createUser({
      phoneNumber: CUSTOMER_USER_CONFIG.phone,
      displayName: `${CUSTOMER_USER_CONFIG.firstName} ${CUSTOMER_USER_CONFIG.lastName}`,
      disabled: false,
    });
    await this.postCreate(user.uid);
  }

  private async update(uid: string): Promise<void> {
    await this.auth.updateUser(uid, {
      displayName: `${CUSTOMER_USER_CONFIG.firstName} ${CUSTOMER_USER_CONFIG.lastName}`,
      phoneNumber: CUSTOMER_USER_CONFIG.phone,
    });
    await this.postCreate(uid);
  }

  private async postCreate(uid: string): Promise<void> {
    const now = Timestamp.now();
    const data: CustomerUserData = {
      profile: {
        ...CUSTOMER_USER_CONFIG.profile,
        createdAt: now,
        updatedAt: now,
      },
      roles: CUSTOMER_USER_CONFIG.roles,
      authentication: {
        phoneVerified: true,
        emailVerified: false,
        providers: ['phone'],
        lastLogin: now,
        activeUntil: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)),
      },
      settings: {
        notifications: { orderUpdates: true, promotions: true, system: true },
        privacy: { shareLocation: true, showProfile: true },
        theme: 'light',
      },
      stats: { totalOrders: 0, totalSpent: 0, averageRating: 5, joinedAt: now },
      status: 'active',
    };

    await this.db.collection('users').doc(uid).set(data, { merge: true });
    await this.auth.setCustomUserClaims(uid, {
      roles: CUSTOMER_USER_CONFIG.roles,
      isCustomer: true,
      permissions: ['create_order', 'view_profile', 'edit_profile', 'view_orders'],
    });
    console.log('✅ Customer user stored/updated, custom claims set');
  }
}

async function main() {
  try {
    const seeder = new PhoneCustomerUserSeeder();
    await seeder.seed();
    console.log('🎉 Phone customer user ready');
  } catch (e) {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { main };


