#!/usr/bin/env tsx

/**
 * Phone Driver User Seeding Script
 *
 * Creates/updates a driver user authenticated by phone, for testing.
 * Phone: +14444444444
 *
 * Usage:
 *  - npx tsx scripts/seed-phone-driver-user.ts
 */

import { initializeApp, cert } from 'firebase-admin/app';
import { getAuth, Auth } from 'firebase-admin/auth';
import { getFirestore, Firestore, Timestamp } from 'firebase-admin/firestore';
import { readFileSync } from 'fs';
import dotenv from 'dotenv';

dotenv.config({ path: '.env.local' });

const DRIVER_USER_CONFIG = {
  phone: '+14444444444',
  firstName: 'Phone',
  lastName: 'Driver',
  roles: ['driver'],
  profile: {
    firstName: 'Phone',
    lastName: 'Driver',
    phone: '+14444444444',
    email: '',
    avatar: '',
    gender: 'male' as const,
    language: 'en' as const,
  }
};

interface DriverUserData {
  profile: {
    firstName: string;
    lastName: string;
    phone: string;
    email: string;
    avatar: string;
    gender: 'male' | 'female';
    language: 'en' | 'ar';
    createdAt: Timestamp;
    updatedAt: Timestamp;
  };
  roles: string[];
  authentication: {
    phoneVerified: boolean;
    emailVerified: boolean;
    providers: string[];
    lastLogin: Timestamp;
    activeUntil: Timestamp;
  };
  settings: {
    notifications: {
      orderUpdates: boolean;
      promotions: boolean;
      system: boolean;
    };
    privacy: {
      shareLocation: boolean;
      showProfile: boolean;
    };
    theme: 'light' | 'dark';
  };
  stats: {
    totalOrders: number;
    totalSpent: number;
    averageRating: number;
    joinedAt: Timestamp;
  };
  status: 'active' | 'inactive' | 'suspended';
}

class PhoneDriverUserSeeder {
  private auth!: Auth;
  private db!: Firestore;
  private initialized = false;

  constructor() {
    this.init();
  }

  private init(): void {
    if (this.initialized) return;

    const serviceAccountPath = process.env.FIREBASE_SERVICE_ACCOUNT_PATH;
    if (!serviceAccountPath) {
      throw new Error('FIREBASE_SERVICE_ACCOUNT_PATH is required');
    }

    const serviceAccount = JSON.parse(readFileSync(serviceAccountPath, 'utf8'));
    initializeApp({
      credential: cert(serviceAccount),
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
      databaseURL: `https://${process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID}.firebaseio.com`,
    });
    this.auth = getAuth();
    this.db = getFirestore();
    this.initialized = true;
    console.log('✅ Firebase Admin initialized');
  }

  async seed(): Promise<void> {
    console.log('🚀 Seeding phone driver user...');
    console.log(`📱 Phone: ${DRIVER_USER_CONFIG.phone}`);

    const existing = await this.findByPhone(DRIVER_USER_CONFIG.phone);
    if (existing) {
      console.log('⚠️  Driver exists, updating...');
      await this.update(existing.uid);
    } else {
      console.log('🆕 Creating driver...');
      await this.create();
    }
  }

  private async findByPhone(phone: string) {
    try {
      return await this.auth.getUserByPhoneNumber(phone);
    } catch (e: any) {
      if (e?.code === 'auth/user-not-found') return null;
      throw e;
    }
  }

  private async create(): Promise<void> {
    const user = await this.auth.createUser({
      phoneNumber: DRIVER_USER_CONFIG.phone,
      displayName: `${DRIVER_USER_CONFIG.firstName} ${DRIVER_USER_CONFIG.lastName}`,
      disabled: false,
    });
    await this.postCreate(user.uid);
  }

  private async update(uid: string): Promise<void> {
    await this.auth.updateUser(uid, {
      displayName: `${DRIVER_USER_CONFIG.firstName} ${DRIVER_USER_CONFIG.lastName}`,
      phoneNumber: DRIVER_USER_CONFIG.phone,
    });
    await this.postCreate(uid);
  }

  private async postCreate(uid: string): Promise<void> {
    const now = Timestamp.now();
    const data: DriverUserData = {
      profile: {
        ...DRIVER_USER_CONFIG.profile,
        createdAt: now,
        updatedAt: now,
      },
      roles: DRIVER_USER_CONFIG.roles,
      authentication: {
        phoneVerified: true,
        emailVerified: false,
        providers: ['phone'],
        lastLogin: now,
        activeUntil: Timestamp.fromDate(new Date(Date.now() + 365 * 24 * 60 * 60 * 1000)),
      },
      settings: {
        notifications: { orderUpdates: true, promotions: true, system: true },
        privacy: { shareLocation: true, showProfile: true },
        theme: 'light',
      },
      stats: { totalOrders: 0, totalSpent: 0, averageRating: 5, joinedAt: now },
      status: 'active',
    };

    await this.db.collection('users').doc(uid).set(data, { merge: true });
    await this.auth.setCustomUserClaims(uid, {
      roles: DRIVER_USER_CONFIG.roles,
      isDriver: true,
      permissions: ['accept_orders', 'complete_orders', 'view_profile', 'edit_profile'],
    });
    console.log('✅ Driver user stored/updated, custom claims set');
  }
}

async function main() {
  try {
    const seeder = new PhoneDriverUserSeeder();
    await seeder.seed();
    console.log('🎉 Phone driver user ready');
  } catch (e) {
    console.error('❌ Seeding failed:', e);
    process.exit(1);
  }
}

if (require.main === module) {
  main().catch(console.error);
}

export { main };


