import { LoginForm } from "@/components/auth/LoginForm";
import { AuthFallback } from "@/components/auth/AuthFallback";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import Link from "next/link";
import { Logo } from "@/components/ui/logo";
import { isFirebaseConfigured } from "@/lib/firebase";

export default function LoginPage() {
	// Show fallback if Firebase is not configured
	if (!isFirebaseConfigured) {
		return <AuthFallback />;
	}

	return (
		<div className="min-h-screen bg-gradient-to-br from-yellow-50 via-white to-yellow-50 flex items-center justify-center p-4">
			<div className="w-full max-w-md space-y-6">
				{/* Logo */}
				<div className="flex items-center justify-center">
					<Logo size="lg" />
				</div>

				{/* Login Card */}
				<Card>
					<CardHeader className="text-center">
						<CardTitle className="text-2xl">Get started with your phone</CardTitle>
						<CardDescription>
							Enter your mobile number to receive a one-time code
						</CardDescription>
					</CardHeader>
					<CardContent>
						<LoginForm />
					</CardContent>
				</Card>

				{/* Footer Links */}
				<div className="text-center">
					<Link href="/" className="text-sm text-gray-500 hover:text-gray-700">
						← Back to Home
					</Link>
				</div>
			</div>
		</div>
	);
}
