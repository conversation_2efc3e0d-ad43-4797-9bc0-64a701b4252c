'use client'

import { useAuth } from '@/hooks/useAuth'
import { useOrders } from '@/hooks/useOrders'
import { useRideRequests } from '@/hooks/useRideRequests'
import { useState, useEffect, useMemo } from 'react'
import { MapPin, Clock, User, Phone, Navigation, CheckCircle, XCircle, Car } from 'lucide-react'
import { OrderDocument, OrderStatus, RideRequestDocument } from '@/types/database'
import { DriverService } from '@/services/DriverService'
import { GeoPoint } from 'firebase/firestore'
import toast from 'react-hot-toast'
import { RideTrackingMap, useLocationTracking } from '@/components/maps/RideTrackingMap'
import useDriverLiveLocation from '@/hooks/useDriverLiveLocation'
import { useRef } from 'react'
import { getRouteCoordinates, getNextPointOnRoute, calculateBearing } from '@/utils/polylineUtils'

// Geolocation utility function
const getCurrentLocation = (): Promise<{ lat: number; lng: number }> => {
  return new Promise((resolve, reject) => {
    if (!navigator.geolocation) {
      reject(new Error('Geolocation is not supported by this browser'))
      return
    }

    navigator.geolocation.getCurrentPosition(
      (position) => {
        resolve({
          lat: position.coords.latitude,
          lng: position.coords.longitude
        })
      },
      (error) => {
        reject(new Error(`Location error: ${error.message}`))
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000 // 5 minutes
      }
    )
  })
}

export default function DriverDashboard() {
  const { userProfile } = useAuth()
  const { orders, updateOrderStatus, subscribeToUserOrders } = useOrders()
  const { requests, acceptRideRequest, rejectRideRequest, subscribeToPendingRequests } = useRideRequests()
  const [isOnline, setIsOnline] = useState(false)
  const [isUpdatingStatus, setIsUpdatingStatus] = useState(false)
  const [activeOrder, setActiveOrder] = useState<OrderDocument | null>(null)
  const [availableRequests, setAvailableRequests] = useState<RideRequestDocument[]>([])
  const { driverLocation } = useLocationTracking(activeOrder?.id)
  const liveDriverLocation = useDriverLiveLocation(userProfile?.id || null)
  const [isTestMode, setIsTestMode] = useState(false)
  const [currentRoutePhase, setCurrentRoutePhase] = useState<'pickup' | 'destination' | null>(null)
  const [routeCoordinates, setRouteCoordinates] = useState<{ lat: number; lng: number }[]>([])
  const [driverBearing, setDriverBearing] = useState<number>(0)
  const simIntervalRef = useRef<NodeJS.Timeout | null>(null)

  // Compute pickup/destination for map when active
  const pickupLocation = useMemo(() => {
    if (!activeOrder) return null
    return {
      lat: activeOrder.locations.pickup.coordinates.latitude,
      lng: activeOrder.locations.pickup.coordinates.longitude
    }
  }, [activeOrder])

  const destinationLocation = useMemo(() => {
    if (!activeOrder) return null
    return {
      lat: activeOrder.locations.destination.coordinates.latitude,
      lng: activeOrder.locations.destination.coordinates.longitude
    }
  }, [activeOrder])

  // Load driver status from Firebase on component mount
  useEffect(() => {
    const loadDriverStatus = async () => {
      if (userProfile?.id) {
        try {
          const driver = await DriverService.getDriverById(userProfile.id)
          if (driver) {
            setIsOnline(driver.status.online)
          } else {
            console.warn('Driver document not found for user:', userProfile.id)
            toast.error('Please complete your driver profile to access dashboard features')
          }
        } catch (error) {
          console.error('Failed to load driver status:', error)
          toast.error('Failed to load driver information')
        }
      }
    }

    loadDriverStatus()
  }, [userProfile?.id])

  // Subscribe to driver orders and ride requests when component mounts
  useEffect(() => {
    if (userProfile?.id) {
      subscribeToUserOrders(userProfile.id, 'driver')
      subscribeToPendingRequests()
    }
  }, [userProfile?.id, subscribeToUserOrders, subscribeToPendingRequests])

  // Handle online status toggle
  const handleToggleOnlineStatus = async () => {
    if (!userProfile?.id || isUpdatingStatus) return

    setIsUpdatingStatus(true)
    const newOnlineStatus = !isOnline

    try {
      // First check if driver document exists
      const driver = await DriverService.getDriverById(userProfile.id)
      if (!driver) {
        toast.error('Driver profile not found. Please complete your driver registration first.')
        return
      }

      // If going online, get current location first
      if (newOnlineStatus) {
        try {
          toast.loading('Getting your location...', { id: 'location-update' })
          const location = await getCurrentLocation()

          // Update driver location
          const geoPoint = new GeoPoint(location.lat, location.lng)
          await DriverService.updateDriverLocation(
            userProfile.id,
            geoPoint,
            0, // heading
            0, // speed
            10 // accuracy in meters
          )

          toast.dismiss('location-update')
          toast.success('Location updated successfully', { duration: 2000 })
          console.log('Location updated:', location)
        } catch (locationError) {
          toast.dismiss('location-update')
          console.warn('Failed to get location:', locationError)

          const errorMessage = locationError instanceof Error ? locationError.message : 'Unknown location error'
          if (errorMessage.includes('denied')) {
            toast.error('Location permission denied. Please enable location access in your browser settings to help customers find you.')
          } else if (errorMessage.includes('unavailable')) {
            toast.error('Location service unavailable. You can still go online, but customers may not see your exact position.')
          } else {
            toast.error('Could not get your location. You can still go online, but customers may not see your exact position.')
          }
        }
      }

      await DriverService.updateDriverStatus(userProfile.id, newOnlineStatus, newOnlineStatus)
      setIsOnline(newOnlineStatus)
      toast.success(newOnlineStatus ? 'You are now online and available for rides' : 'You are now offline')
    } catch (error) {
      console.error('Failed to update driver status:', error)
      if (error instanceof Error) {
        toast.error(`Failed to update status: ${error.message}`)
      } else {
        toast.error('Failed to update status. Please try again.')
      }
    } finally {
      setIsUpdatingStatus(false)
    }
  }

  // Separate active orders and available ride requests
  useEffect(() => {
    const active = orders.find(order => 
      order.driver?.id === userProfile?.id &&
      ['assigned', 'driver_arriving', 'driver_arrived', 'picked_up', 'in_progress'].includes(order.status.current)
    )
    setActiveOrder(active || null)

    const available = requests.filter(request => 
      request.status.current === 'pending'
    ).slice(0, 5)
    setAvailableRequests(available)
  }, [orders, requests, userProfile?.id])

  // Update route phase based on order status
  useEffect(() => {
    if (!activeOrder) {
      setCurrentRoutePhase(null)
      setRouteCoordinates([])
      return
    }

    const status = activeOrder.status.current
    if (['assigned', 'driver_arriving', 'driver_arrived'].includes(status)) {
      setCurrentRoutePhase('pickup')
    } else if (['picked_up', 'in_progress'].includes(status)) {
      setCurrentRoutePhase('destination')
    } else {
      setCurrentRoutePhase(null)
      setRouteCoordinates([])
    }
  }, [activeOrder?.status.current])

  // Load route coordinates when route phase changes
  useEffect(() => {
    if (!currentRoutePhase || !activeOrder) {
      setRouteCoordinates([])
      return
    }

    const loadRoute = async () => {
      try {
        let origin: { lat: number; lng: number }
        let destination: { lat: number; lng: number }

        if (currentRoutePhase === 'pickup') {
          // Get current driver location or use a default
          const currentLocation = liveDriverLocation || driverLocation || { lat: 31.9539, lng: 35.9106 }
          origin = currentLocation
          destination = {
            lat: activeOrder.locations.pickup.coordinates.latitude,
            lng: activeOrder.locations.pickup.coordinates.longitude
          }
        } else {
          // Moving from pickup to destination - use driver's current location as origin
          const currentLocation = liveDriverLocation || driverLocation || {
            lat: activeOrder.locations.pickup.coordinates.latitude,
            lng: activeOrder.locations.pickup.coordinates.longitude
          }
          origin = currentLocation
          destination = {
            lat: activeOrder.locations.destination.coordinates.latitude,
            lng: activeOrder.locations.destination.coordinates.longitude
          }
        }

        console.log(`Loading route for ${currentRoutePhase} phase:`, { origin, destination })
        const coordinates = await getRouteCoordinates(origin, destination)
        console.log(`Route loaded with ${coordinates.length} points`)
        setRouteCoordinates(coordinates)
      } catch (error) {
        console.error('Failed to load route coordinates:', error)
        setRouteCoordinates([])
      }
    }

    loadRoute()
  }, [currentRoutePhase, activeOrder, liveDriverLocation, driverLocation])

  const handleAcceptRequest = async (requestId: string) => {
    if (!userProfile) return

    try {
      const driverData = {
        name: `${userProfile.profile.firstName} ${userProfile.profile.lastName}`,
        phone: userProfile.profile.phone,
        avatar: userProfile.profile.avatar || '',
        vehicle: {
          make: 'Toyota',
          model: 'Camry',
          year: 2020,
          color: 'White',
          plateNumber: 'JO-1234-A',
          type: 'standard'
        },
        location: { lat: 31.9539, lng: 35.9106 }
      }

      await acceptRideRequest(requestId, userProfile.id, driverData)
      toast.success('Ride request accepted! Order created and you are now assigned.')
    } catch (error) {
      toast.error('Failed to accept ride request')
    }
  }

  const handleRejectRequest = async (requestId: string) => {
    try {
      await rejectRideRequest(requestId, userProfile?.id || '', 'Driver rejected the request')
      toast.success('Ride request rejected')
    } catch (error) {
      toast.error('Failed to reject ride request')
    }
  }

  const handleStatusUpdate = async (orderId: string, status: OrderStatus) => {
    try {
      await updateOrderStatus(orderId, status)
      const statusMessages: Partial<Record<OrderStatus, string>> = {
        driver_arriving: 'Marked as arriving to pickup',
        driver_arrived: 'Marked as arrived at pickup',
        picked_up: 'Passenger picked up',
        in_progress: 'Trip started',
        completed: 'Trip completed successfully'
      }
      toast.success(statusMessages[status] ?? 'Status updated')
    } catch {
      toast.error('Failed to update status')
    }
  }

  // Test Mode simulation: move driver toward pickup
  useEffect(() => {
    // Clean up on unmount
    return () => {
      if (simIntervalRef.current) {
        clearInterval(simIntervalRef.current)
        simIntervalRef.current = null
      }
    }
  }, [])

  useEffect(() => {
    // Stop simulation if no active order, test mode off, no route phase, or no route coordinates
    if (!isTestMode || !activeOrder || !userProfile?.id || !currentRoutePhase || routeCoordinates.length === 0) {
      if (simIntervalRef.current) {
        clearInterval(simIntervalRef.current)
        simIntervalRef.current = null
      }
      return
    }

    const stepMeters = 60 // move ~60m per tick
    const tickMs = 2000 // every 2s

    // Start interval
    simIntervalRef.current = setInterval(async () => {
      try {
        // Current driver coordinates from live feed if available, else from order's driver location or bail
        const current = (liveDriverLocation) || (driverLocation) || null
        if (!current) return

        // Get next point on the route
        const nextPoint = getNextPointOnRoute(current, routeCoordinates, stepMeters)
        if (!nextPoint) {
          // Reached end of route
          console.log('Reached end of route, stopping simulation')
          if (simIntervalRef.current) {
            clearInterval(simIntervalRef.current)
            simIntervalRef.current = null
          }
          return
        }

        console.log(`Moving from ${current.lat.toFixed(6)}, ${current.lng.toFixed(6)} to ${nextPoint.lat.toFixed(6)}, ${nextPoint.lng.toFixed(6)}`)

        // Calculate bearing for car rotation
        const bearing = calculateBearing(current, nextPoint)
        setDriverBearing(bearing)
        console.log(`Car bearing: ${bearing.toFixed(1)}°`)

        const geoPoint = new GeoPoint(nextPoint.lat, nextPoint.lng)
        await DriverService.updateDriverLocation(userProfile.id, geoPoint, bearing, 0, 10)

        // Check if we're close to the final destination
        const finalDestination = currentRoutePhase === 'pickup' 
          ? {
              lat: activeOrder.locations.pickup.coordinates.latitude,
              lng: activeOrder.locations.pickup.coordinates.longitude
            }
          : {
              lat: activeOrder.locations.destination.coordinates.latitude,
              lng: activeOrder.locations.destination.coordinates.longitude
            }

        const R = 6371000
        const dLat = (finalDestination.lat - nextPoint.lat) * Math.PI / 180
        const dLng = (finalDestination.lng - nextPoint.lng) * Math.PI / 180
        const a = Math.sin(dLat / 2) ** 2 + Math.cos(nextPoint.lat * Math.PI / 180) * Math.cos(finalDestination.lat * Math.PI / 180) * Math.sin(dLng / 2) ** 2
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
        const dist = R * c

        if (dist < 30) {
          // Close to destination, stop simulation for this phase
          if (simIntervalRef.current) {
            clearInterval(simIntervalRef.current)
            simIntervalRef.current = null
          }
        }
      } catch (e) {
        console.warn('Test mode location update failed', e)
      }
    }, tickMs)

    return () => {
      if (simIntervalRef.current) {
        clearInterval(simIntervalRef.current)
        simIntervalRef.current = null
      }
    }
  }, [isTestMode, activeOrder, userProfile?.id, liveDriverLocation, driverLocation, currentRoutePhase, routeCoordinates])

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-[calc(100vh-0px)]">
      {/* Full-page map */}
      <div className="absolute inset-0">
        {activeOrder && pickupLocation && destinationLocation ? (
          <RideTrackingMap
            pickup={pickupLocation}
            destination={destinationLocation}
            showRoute={true}
            driverLocation={(liveDriverLocation || driverLocation) || undefined}
            driverBearing={driverBearing}
            className="h-full"
          />
        ) : (
          <div className="w-full h-full bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">No active ride. Stay online to receive requests.</p>
            </div>
          </div>
        )}
        </div>
        
      {/* Overlays */}
      <div className="fixed top-4 right-4 z-30">
        {/* Online/Offline Toggle */}
        <div className="glass-effect rounded-xl border border-white/20 p-3 flex items-center space-x-3">
          <span className="text-sm font-medium text-gray-700">
            {isOnline ? 'Online' : 'Offline'}
          </span>
          <button
            onClick={handleToggleOnlineStatus}
            disabled={isUpdatingStatus}
            className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
              isOnline ? 'bg-green-500' : 'bg-gray-300'
            } ${isUpdatingStatus ? 'opacity-50 cursor-not-allowed' : ''}`}
          >
            <span
              className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                isOnline ? 'translate-x-6' : 'translate-x-1'
              }`}
            />
          </button>
          {isUpdatingStatus && (
            <span className="text-xs text-gray-500">Updating...</span>
          )}
          {/* Test Mode Button */}
          <button
            onClick={() => setIsTestMode(prev => !prev)}
            className={`ml-2 px-3 py-1 rounded-lg text-xs font-medium ${isTestMode ? 'bg-yellow-500 text-black' : 'bg-gray-200 text-gray-700'} hover:opacity-90`}
            title="Simulate movement toward pickup"
          >
            {isTestMode ? 'Test Mode: ON' : 'Test Mode'}
          </button>
        </div>
      </div>

      {/* Active Order Card */}
      {activeOrder && (
        <div className="fixed inset-x-0 bottom-0 md:left-20 md:right-0 md:inset-x-auto z-30 p-4">
          <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/40 p-4 md:p-5 max-w-4xl mx-auto">
          <div className="flex items-center justify-between mb-4">
              <h2 className="text-lg md:text-xl font-semibold text-gray-900">Active Ride</h2>
              <span className="px-3 py-1 rounded-full text-xs md:text-sm font-medium bg-blue-100 text-blue-800 capitalize">{activeOrder.status.current.replace('_',' ')}</span>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Trip Details */}
              <div className="space-y-3 md:space-y-4">
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 rounded-full bg-green-500 mt-2"></div>
                  <div className="min-w-0">
                    <p className="text-xs md:text-sm text-gray-500">Pickup</p>
                    <p className="font-medium text-gray-900 text-sm md:text-base break-words">{activeOrder.locations.pickup.address}</p>
                </div>
              </div>
              <div className="flex items-start space-x-3">
                <div className="w-3 h-3 rounded-full bg-red-500 mt-2"></div>
                  <div className="min-w-0">
                    <p className="text-xs md:text-sm text-gray-500">Destination</p>
                    <p className="font-medium text-gray-900 text-sm md:text-base break-words">{activeOrder.locations.destination.address}</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <Clock className="w-4 h-4 text-gray-400" />
                <div>
                    <p className="text-xs md:text-sm text-gray-500">Fare</p>
                    <p className="font-medium text-gray-900 text-sm md:text-base">JD {activeOrder.pricing.total.toFixed(2)}</p>
                </div>
              </div>
            </div>

              {/* Customer Actions */}
              <div className="space-y-3 md:space-y-4">
              <div className="flex items-center space-x-3">
                  <div className="w-10 h-10 md:w-12 md:h-12 rounded-full bg-blue-100 flex items-center justify-center">
                  {activeOrder.customer.avatar ? (
                    <img 
                      src={activeOrder.customer.avatar} 
                      alt={activeOrder.customer.name}
                        className="w-10 h-10 md:w-12 md:h-12 rounded-full object-cover"
                    />
                  ) : (
                      <User className="w-5 h-5 md:w-6 md:h-6 text-blue-600" />
                  )}
                </div>
                  <div className="min-w-0">
                    <p className="font-medium text-gray-900 text-sm md:text-base">{activeOrder.customer.name}</p>
                    <p className="text-xs md:text-sm text-gray-500">Customer</p>
                </div>
              </div>

                <div className="grid grid-cols-2 gap-2">
                  <button className="bg-green-500 text-white px-3 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center text-xs md:text-sm">
                    <Phone className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                    Call
                </button>
                  <button className="bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-600 transition-colors flex items-center justify-center text-xs md:text-sm">
                    <Navigation className="w-3 h-3 md:w-4 md:h-4 mr-1 md:mr-2" />
                  Navigate
                </button>
              </div>

              {/* Status Update Button */}
              {(() => {
                  const nextAction = (() => {
                    switch (activeOrder.status.current) {
                      case 'assigned': return { status: 'driver_arriving' as OrderStatus, text: 'Start Driving to Pickup' }
                      case 'driver_arriving': return { status: 'driver_arrived' as OrderStatus, text: 'Mark as Arrived' }
                      case 'driver_arrived': return { status: 'picked_up' as OrderStatus, text: 'Pick Up Passenger' }
                      case 'picked_up': return { status: 'in_progress' as OrderStatus, text: 'Start Trip' }
                      case 'in_progress': return { status: 'completed' as OrderStatus, text: 'Complete Trip' }
                      default: return null
                    }
                  })()
                return nextAction ? (
                  <button
                    onClick={() => handleStatusUpdate(activeOrder.id, nextAction.status)}
                    className="w-full bg-yellow-500 text-black px-4 py-2 rounded-lg hover:bg-yellow-600 transition-colors font-medium"
                  >
                    {nextAction.text}
                  </button>
                ) : null
              })()}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Available Requests Panel */}
      {isOnline && !activeOrder && (
        <div className="fixed inset-x-0 bottom-0 md:left-20 md:right-0 md:inset-x-auto z-30 p-4">
          <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/40 p-4 md:p-5 max-w-4xl mx-auto">
            <h2 className="text-lg md:text-xl font-semibold text-gray-900 mb-4">Available Ride Requests</h2>
          {availableRequests.length === 0 ? (
            <div className="text-center py-8">
              <Car className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500">No ride requests available</p>
              <p className="text-sm text-gray-400">Stay online to receive ride requests</p>
            </div>
          ) : (
              <div className="space-y-3 md:space-y-4">
              {availableRequests.map((request) => (
                  <div key={request.id} className="bg-white/50 rounded-xl p-3 md:p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 md:w-10 md:h-10 rounded-full bg-blue-100 flex items-center justify-center">
                          <User className="w-4 h-4 md:w-5 md:h-5 text-blue-600" />
                      </div>
                      <div>
                          <p className="font-medium text-gray-900 text-sm md:text-base">{request.customer.name}</p>
                          <p className="text-xs md:text-sm text-gray-500">JD {request.pricing.total.toFixed(2)}</p>
                      </div>
                    </div>
                    <div className="text-right">
                        <p className="text-xs md:text-sm text-gray-500">Distance</p>
                        <p className="font-medium text-gray-900 text-sm md:text-base">{request.locations.route?.distance?.toFixed(1) || 'N/A'} km</p>
                    </div>
                  </div>

                    <div className="space-y-2 mb-3 md:mb-4">
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-green-500"></div>
                      <p className="text-sm text-gray-700">{request.locations.pickup.address}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 rounded-full bg-red-500"></div>
                      <p className="text-sm text-gray-700">{request.locations.destination.address}</p>
                    </div>
                  </div>

                    <div className="grid grid-cols-2 gap-2">
                    <button
                      onClick={() => handleAcceptRequest(request.id)}
                        className="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors flex items-center justify-center text-sm"
                    >
                      <CheckCircle className="w-4 h-4 mr-2" />
                      Accept
                    </button>
                    <button
                      onClick={() => handleRejectRequest(request.id)}
                        className="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors flex items-center justify-center text-sm"
                    >
                      <XCircle className="w-4 h-4 mr-2" />
                      Decline
                    </button>
                  </div>
                </div>
              ))}
            </div>
          )}
          </div>
        </div>
      )}

      {/* Offline Message */}
      {!isOnline && (
        <div className="fixed inset-x-0 bottom-0 md:left-20 md:right-0 md:inset-x-auto z-30 p-4">
          <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/40 p-6 text-center max-w-2xl mx-auto">
            <Car className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg md:text-xl font-semibold text-gray-900 mb-2">You&apos;re Offline</h3>
            <p className="text-gray-600 mb-4 text-sm md:text-base">
            Turn on your availability to start receiving ride requests
          </p>
          <button
            onClick={handleToggleOnlineStatus}
            disabled={isUpdatingStatus}
              className="bg-yellow-500 text-black px-5 py-2 rounded-xl font-semibold hover:bg-yellow-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed text-sm md:text-base"
          >
            {isUpdatingStatus ? 'Updating...' : 'Go Online'}
          </button>
          </div>
        </div>
      )}
    </div>
  )
}
