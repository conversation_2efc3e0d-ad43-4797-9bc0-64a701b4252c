'use client'

import { useAuth } from '@/hooks/useAuth'
import { useOrders } from '@/hooks/useOrders'
import { useEffect, useMemo } from 'react'
import { History } from 'lucide-react'

export default function RidesHistoryPage() {
  const { userProfile } = useAuth()
  const { orders, subscribeToUserOrders } = useOrders()

  useEffect(() => {
    if (userProfile?.id) {
      subscribeToUserOrders(userProfile.id, 'customer')
    }
  }, [userProfile?.id, subscribeToUserOrders])

  const completedOrders = useMemo(() => {
    return orders
      .filter(o => ['completed','cancelled'].includes(o.status.current))
      .sort((a, b) => (b.metadata?.createdAt?.toDate().getTime() || 0) - (a.metadata?.createdAt?.toDate().getTime() || 0))
  }, [orders])

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl md:text-3xl font-bold text-gray-900">Ride History</h1>
      </div>

      <div className="glass-effect rounded-2xl p-4 md:p-6 border border-white/20">
        {completedOrders.length === 0 ? (
          <div className="text-center py-12">
            <History className="w-16 h-16 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-500 text-lg">No ride history yet</p>
            <p className="text-sm text-gray-400">Your completed and cancelled rides will appear here</p>
          </div>
        ) : (
          <div className="space-y-3 md:space-y-4">
            {completedOrders.map(order => (
              <div key={order.id} className="flex flex-col sm:flex-row sm:items-center justify-between p-3 md:p-4 bg-white/50 rounded-xl hover:bg-white/70 transition-colors space-y-3 sm:space-y-0">
                <div className="flex items-start space-x-3 md:space-x-4">
                  <div className="flex flex-col items-center flex-shrink-0">
                    <div className="w-2 h-2 rounded-full bg-green-500"></div>
                    <div className="w-px h-4 md:h-6 bg-gray-300"></div>
                    <div className="w-2 h-2 rounded-full bg-red-500"></div>
                  </div>
                  <div className="min-w-0 flex-1">
                    <p className="font-medium text-gray-900 text-sm md:text-base break-words">
                      {order.locations.pickup.address} → {order.locations.destination.address}
                    </p>
                    <p className="text-xs md:text-sm text-gray-500">
                      {order.metadata?.createdAt?.toDate().toLocaleDateString()} at {order.metadata?.createdAt?.toDate().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                    </p>
                  </div>
                </div>
                <div className="flex items-center justify-between sm:justify-end space-x-3">
                  <div className="text-left sm:text-right">
                    <p className="font-medium text-gray-900 text-sm md:text-base">JD {order.pricing.total.toFixed(2)}</p>
                    <span className="px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 capitalize">{order.status.current}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  )
}


