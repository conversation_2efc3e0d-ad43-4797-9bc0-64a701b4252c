'use client'

import { useAuth } from '@/hooks/useAuth'
import { useOrders } from '@/hooks/useOrders'
import { useRideRequests } from '@/hooks/useRideRequests'
import { useNearbyDrivers } from '@/hooks/useNearbyDrivers'
import { useEffect, useMemo, useState } from 'react'
import { RideTrackingMap } from '@/components/maps/RideTrackingMap'
import { RideTrackingMap as RideMapWithDriver, useLocationTracking } from '@/components/maps/RideTrackingMap'
import { LocationSearch } from '@/components/maps/LocationSearch'
import NearbyDriversMap from '@/components/maps/NearbyDriversMap'
import { OrderDocument, RideRequestDocument } from '@/types/database'
import { CreateRideRequestData } from '@/services/RideRequestService'
import { AlertTriangle, Car, MapPin, RefreshCw, X } from 'lucide-react'
import toast from 'react-hot-toast'
import { RideCompletionModal } from '@/components/dashboard/RideCompletionModal'
import useDriverLiveLocation from '@/hooks/useDriverLiveLocation'

export default function RidesPage() {
  const { userProfile } = useAuth()
  const { orders, subscribeToUserOrders, subscribeToOrder, updateOrderStatus } = useOrders()
  const {
    requests,
    createRideRequest,
    subscribeToCustomerRequests,
    deleteRideRequest,
    cancelRideRequest
  } = useRideRequests()

  const [activeOrder, setActiveOrder] = useState<OrderDocument | null>(null)
  const [activeRequest, setActiveRequest] = useState<RideRequestDocument | null>(null)
  const { driverLocation } = useLocationTracking(activeOrder?.id)
  const liveDriverLocation = useDriverLiveLocation(activeOrder?.driver?.id || null)

  // Subscribe to orders and requests
  useEffect(() => {
    if (!userProfile?.id) return
    subscribeToUserOrders(userProfile.id, 'customer')
    subscribeToCustomerRequests(userProfile.id)
  }, [userProfile?.id, subscribeToUserOrders, subscribeToCustomerRequests])

  // Track active order/request
  useEffect(() => {
    const active = orders.find(o => ['pending','searching','assigned','driver_arriving','driver_arrived','picked_up','in_progress'].includes(o.status.current))
    setActiveOrder(active || null)
  }, [orders])

  // Subscribe to the active order for live updates
  useEffect(() => {
    if (activeOrder?.id) {
      subscribeToOrder(activeOrder.id)
    }
  }, [activeOrder?.id, subscribeToOrder])

  useEffect(() => {
    const active = requests.find(r => ['pending','accepted'].includes(r.status.current))
    setActiveRequest(active || null)
  }, [requests])

  const pickupLocation = useMemo(() => {
    if (!activeOrder) return null
    return {
      lat: activeOrder.locations.pickup.coordinates.latitude,
      lng: activeOrder.locations.pickup.coordinates.longitude,
    }
  }, [activeOrder])

  const destinationLocation = useMemo(() => {
    if (!activeOrder) return null
    return {
      lat: activeOrder.locations.destination.coordinates.latitude,
      lng: activeOrder.locations.destination.coordinates.longitude,
    }
  }, [activeOrder])

  // Bottom booking inputs state
  const [pickup, setPickup] = useState<{address: string, coordinates: {lat: number, lng: number}} | null>(null)
  const [destination, setDestination] = useState<{address: string, coordinates: {lat: number, lng: number}} | null>(null)

  const { drivers, isLoading: driversLoading, error: driversError, refreshDrivers, lastUpdated } = useNearbyDrivers(
    pickup ? pickup.coordinates : null,
    { radiusKm: 10, autoRefresh: true, refreshInterval: 30000 }
  )

  const handleBook = async () => {
    if (!userProfile?.id) return
    if (!pickup || !destination) {
      toast.error('Please select both pickup and destination locations')
      return
    }
    const requestData: CreateRideRequestData = {
      customerId: userProfile.id,
      pickup: { address: pickup.address, coordinates: pickup.coordinates },
      destination: { address: destination.address, coordinates: destination.coordinates },
      // No serviceType per requirement
      serviceType: 'standard',
      paymentMethod: 'cash',
    }
    try {
      await createRideRequest(requestData)
      toast.success('Ride request sent! Looking for nearby drivers...')
    } catch {
      toast.error('Failed to send ride request')
    }
  }

  const handleCancelRequest = async () => {
    if (!activeRequest) return
    try {
      await cancelRideRequest(activeRequest.id, 'Cancelled by customer')
      toast.success('Ride request cancelled')
    } catch {
      toast.error('Failed to cancel ride request')
    }
  }

  const handleCancelOrder = async () => {
    if (!activeOrder) return
    try {
      await updateOrderStatus(activeOrder.id, 'cancelled', 'Cancelled by customer')
      toast.success('Ride cancelled')
    } catch {
      toast.error('Failed to cancel ride')
    }
  }

  // Ride completion modal logic
  const [showCompletionModal, setShowCompletionModal] = useState(false)
  const [completedOrder, setCompletedOrder] = useState<OrderDocument | null>(null)
  const [shownCompletedOrderIds, setShownCompletedOrderIds] = useState<Set<string>>(new Set())

  useEffect(() => {
    const justCompleted = orders.find(order => {
      if (shownCompletedOrderIds.has(order.id)) return false
      const latestEvent = [...order.status.timeline].sort((a, b) => b.timestamp.toDate().getTime() - a.timestamp.toDate().getTime())[0]
      return order.status.current === 'completed' && latestEvent?.status === 'completed' && (Date.now() - latestEvent.timestamp.toDate().getTime() < 60000)
    })

    if (justCompleted && !showCompletionModal) {
      setShownCompletedOrderIds(prev => new Set(prev).add(justCompleted.id))
      setCompletedOrder(justCompleted)
      setShowCompletionModal(true)
    }
  }, [orders, showCompletionModal, shownCompletedOrderIds])

  if (!userProfile) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="relative h-[calc(100vh-0px)]">{/* account for sidebar on md+ */}
      {/* Ride Completion Modal */}
      <RideCompletionModal 
        order={completedOrder} 
        open={showCompletionModal} 
        onClose={() => {
          setShowCompletionModal(false)
          if (completedOrder) {
            const matchingRequest = requests.find(req => 
              req.customer.id === completedOrder.customer.id && 
              req.locations.pickup.address === completedOrder.locations.pickup.address && 
              req.locations.destination.address === completedOrder.locations.destination.address
            )
            if (matchingRequest) {
              deleteRideRequest(matchingRequest.id).catch(() => {})
            }
          }
          setCompletedOrder(null)
        }}
      />
      {/* Full-page map content */}
      <div className="absolute inset-0">
        {/* If there is an active order, track it; otherwise show nearby drivers when pickup selected */}
        {activeOrder && pickupLocation && destinationLocation ? (
          <RideMapWithDriver
            pickup={pickupLocation}
            destination={destinationLocation}
            showRoute={true}
            driverLocation={(liveDriverLocation || driverLocation) || undefined}
            driverBearing={liveDriverLocation?.bearing || 0}
            className="h-full"
          />
        ) : pickup ? (
          <NearbyDriversMap
            pickupLocation={pickup.coordinates}
            drivers={drivers}
            className="h-full"
            showDriverInfo={true}
          />
        ) : (
          <div className="w-full h-full bg-gray-50 flex items-center justify-center">
            <div className="text-center">
              <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
              <p className="text-gray-600">Select pickup to view nearby drivers</p>
            </div>
          </div>
        )}

        {/* Map overlays for drivers info/errors */}
        {!activeOrder && pickup && (
          <div className="absolute top-4 right-4 space-y-2">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg text-sm">
              <div className="flex items-center space-x-2">
                <Car className="w-4 h-4 text-yellow-500" />
                <span className="text-gray-900">{drivers.length} driver{drivers.length !== 1 ? 's' : ''} nearby</span>
                <button
                  onClick={refreshDrivers}
                  className="p-1 text-gray-400 hover:text-gray-600"
                  title="Refresh"
                >
                  <RefreshCw className={`w-4 h-4 ${driversLoading ? 'animate-spin' : ''}`} />
                </button>
              </div>
              {driversError && (
                <div className="mt-2 flex items-center text-red-600">
                  <AlertTriangle className="w-4 h-4 mr-1" /> {driversError}
                </div>
              )}
            </div>
          </div>
        )}
      </div>

      {/* Bottom-fixed inputs (no active ride/request) */}
      {!activeOrder && !activeRequest && (
        <div className="fixed inset-x-0 bottom-0 md:left-20 md:right-0 md:inset-x-auto z-30 p-4">
          <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/40 p-4 md:p-5 max-w-3xl mx-auto">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Pickup Location</label>
                <LocationSearch
                  placeholder="Enter pickup address"
                  value={pickup?.address || ''}
                  onChange={setPickup}
                  className="w-full"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Destination</label>
                <LocationSearch
                  placeholder="Enter destination address"
                  value={destination?.address || ''}
                  onChange={setDestination}
                  className="w-full"
                />
              </div>
            </div>

            <div className="flex items-center justify-between mt-3">
              <div className="text-xs text-gray-500">
                {pickup ? (lastUpdated ? `Drivers updated ${lastUpdated.toLocaleTimeString()}` : 'Drivers loading...') : 'Set pickup to see drivers'}
              </div>
              <div className="flex items-center space-x-2">
                <button
                  onClick={() => { setPickup(null); setDestination(null) }}
                  className="px-3 py-2 text-sm rounded-lg bg-gray-100 hover:bg-gray-200 text-gray-700"
                >
                  <X className="w-4 h-4 inline mr-1" /> Clear
                </button>
                <button
                  onClick={handleBook}
                  disabled={!pickup || !destination}
                  className="px-4 py-2 rounded-lg bg-yellow-500 hover:bg-yellow-600 disabled:opacity-50 text-black font-medium"
                >
                  Book Ride
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Bottom-fixed active request panel */}
      {!activeOrder && activeRequest && (
        <div className="fixed inset-x-0 bottom-0 md:left-20 md:right-0 md:inset-x-auto z-30 p-4">
          <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/40 p-4 md:p-5 max-w-3xl mx-auto">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-base md:text-lg font-semibold text-gray-900">Ride Request</h3>
              <span className="px-3 py-1 rounded-full text-xs md:text-sm font-medium bg-yellow-100 text-yellow-800">{activeRequest.status.current === 'pending' ? 'Looking for driver' : activeRequest.status.current}</span>
            </div>
            <div className="flex items-start space-x-3 text-sm">
              <div className="flex flex-col items-center flex-shrink-0 pt-1">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <div className="w-px h-4 bg-gray-300"></div>
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
              </div>
              <div className="min-w-0 flex-1">
                <p className="font-medium text-gray-900 break-words">{activeRequest.locations.pickup.address} → {activeRequest.locations.destination.address}</p>
                <p className="text-gray-500 mt-1">ETA: {activeRequest.locations.route?.duration || 0} min • Distance: {activeRequest.locations.route?.distance?.toFixed(1) || 0} km</p>
              </div>
              <button onClick={handleCancelRequest} className="px-3 py-2 text-sm rounded-lg bg-red-500 hover:bg-red-600 text-white">Cancel</button>
            </div>
          </div>
        </div>
      )}

      {/* Bottom-fixed active order panel */}
      {activeOrder && (
        <div className="fixed inset-x-0 bottom-0 md:left-20 md:right-0 md:inset-x-auto z-30 p-4">
          <div className="bg-white/95 backdrop-blur-md rounded-2xl shadow-2xl border border-white/40 p-4 md:p-5 max-w-3xl mx-auto">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-base md:text-lg font-semibold text-gray-900">Current Ride</h3>
              <span className="px-3 py-1 rounded-full text-xs md:text-sm font-medium bg-blue-100 text-blue-800 capitalize">{activeOrder.status.current.replace('_',' ')}</span>
            </div>
            <div className="flex items-start space-x-3 text-sm">
              <div className="flex flex-col items-center flex-shrink-0 pt-1">
                <div className="w-2 h-2 rounded-full bg-green-500"></div>
                <div className="w-px h-4 bg-gray-300"></div>
                <div className="w-2 h-2 rounded-full bg-red-500"></div>
              </div>
              <div className="min-w-0 flex-1">
                <p className="font-medium text-gray-900 break-words">{activeOrder.locations.pickup.address} → {activeOrder.locations.destination.address}</p>
                <p className="text-gray-500 mt-1">Fare: JD {activeOrder.pricing.total.toFixed(2)}</p>
              </div>
              {['pending','searching','assigned','driver_arriving'].includes(activeOrder.status.current) && (
                <button onClick={handleCancelOrder} className="px-3 py-2 text-sm rounded-lg bg-red-500 hover:bg-red-600 text-white">Cancel</button>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
  )
}


