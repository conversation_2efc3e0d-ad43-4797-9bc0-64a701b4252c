"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Loader2 } from "lucide-react";
import { phoneAuthService } from "@/services/AuthService";
import { FirebaseConfigWarning } from "./FirebaseConfigWarning";
import { isFirebaseConfigured } from "@/lib/firebase";

import { 
  isValidPhoneForCountry, 
  formatPhoneNumber, 
  COUNTRY_PHONE_CODES,
  getPhonePlaceholder,
  type CountryPhoneInfo
} from "@/lib/utils";

export function LoginForm() {
  const [isLoading, setIsLoading] = useState(false);
  const [isSendingOTP, setIsSendingOTP] = useState(false);
  const [error, setError] = useState("");
  const [phone, setPhone] = useState("");
  const [selectedCountry, setSelectedCountry] = useState<CountryPhoneInfo>(COUNTRY_PHONE_CODES[0]); // Default to Jordan
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFirebaseConfigured) {
      setError("Firebase authentication is not configured. Please contact support.");
      return;
    }
    
    setIsLoading(true);
    setError("");
    
    try {
      // Phone-only login flow
      if (!phone.trim()) {
        setError('Please enter your phone number.');
        return;
      }
      
      if (!isValidPhoneForCountry(phone, selectedCountry.code)) {
        setError(`Please enter a valid ${selectedCountry.name} phone number (e.g., ${selectedCountry.example}).`);
        return;
      }
      
      setIsSendingOTP(true);
      setError('');
      
      const fullPhone = formatPhoneNumber(phone, selectedCountry.code);
      console.log('Formatted phone number:', fullPhone);
      await phoneAuthService.sendOTP(fullPhone);
      
      // Store session metadata for the verification page
      sessionStorage.setItem('phoneAuth_confirmationResult', JSON.stringify({
        phoneNumber: fullPhone,
        timestamp: Date.now(),
        hasActiveSession: true
      }));
      
      // Redirect to OTP verification page
      router.push(`/verify-otp?phone=${encodeURIComponent(fullPhone)}`);
    } catch (error: unknown) {
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
      setIsSendingOTP(false);
    }
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      {/* Phone Login Fields */}
        <div className="space-y-2">
          <Label htmlFor="phone">Phone Number</Label>
          <div className="flex">
            {/* Country Selector */}
            <div className="relative">
              <select
                value={selectedCountry.code}
                onChange={(e) => {
                  const country = COUNTRY_PHONE_CODES.find(c => c.code === e.target.value);
                  if (country) {
                    setSelectedCountry(country);
                    setPhone(''); // Clear phone when country changes
                  }
                }}
                className="flex items-center px-3 py-2 border border-r-0 rounded-l-md bg-gray-50 text-sm text-gray-600 cursor-pointer hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:border-yellow-400 min-w-[120px]"
              >
                {COUNTRY_PHONE_CODES.map((country) => (
                  <option key={country.code} value={country.code}>
                    {country.flag} {country.code} ({country.name})
                  </option>
                ))}
              </select>
            </div>
            <Input
              id="phone"
              type="tel"
              placeholder={getPhonePlaceholder(selectedCountry.code)}
              className="rounded-l-none"
              value={phone}
              onChange={(e) => setPhone(e.target.value)}
              required
            />
          </div>
          {/* Country Format Info */}
          <div className="text-xs text-gray-500">
            {selectedCountry.name} format: {selectedCountry.placeholder}
          </div>
          
          {phone && (
            <div className={`text-xs ${isValidPhoneForCountry(phone, selectedCountry.code) ? 'text-green-600' : 'text-red-600'}`}>
              {isValidPhoneForCountry(phone, selectedCountry.code) 
                ? `✓ Valid ${selectedCountry.name} phone number format` 
                : `Please enter a valid ${selectedCountry.name} phone number (e.g., ${selectedCountry.example})`
              }
            </div>
          )}
        </div>



      {/* Submit Button */}
      <Button
        type="submit"
        className="w-full bg-yellow-400 hover:bg-yellow-500 text-black"
        disabled={isLoading || isSendingOTP}
      >
        {isLoading || isSendingOTP ? (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            Sending OTP...
          </>
        ) : (
          "Send OTP"
        )}
      </Button>

      {/* Error Message */}
      {error && (
        <div className="text-red-600 text-sm text-center bg-red-50 p-3 rounded-md">
          {error}
        </div>
      )}

      {/* No alternative login methods - phone only */}

      
      {/* Firebase Configuration Warning */}
      <FirebaseConfigWarning />
      

    </form>
  );
}
