'use client'

import { useAuth } from '@/hooks/useAuth'
import { usePathname } from 'next/navigation'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import { Logo } from '@/components/ui/logo'
import { useState } from 'react'
import { UserRole } from '@/types/database'
import {
  Home,
  Users,
  Car,
  FileText,
  CreditCard,
  Settings,
  BarChart3,
  MapPin,
  Navigation,
  History as HistoryIcon,
  Menu,
  X
} from 'lucide-react'

type NavItem = { name: string; href: string; icon: React.ComponentType<{ className?: string; strokeWidth?: number }>; roles?: UserRole[] }
const navigation: NavItem[] = [
  { name: 'Dashboard', href: '/dashboard', icon: Home },
  { name: 'Rides', href: '/rides', icon: MapPin, roles: ['customer'] },
  { name: 'History', href: '/rides/history', icon: HistoryIcon, roles: ['customer'] },
  { name: 'Rides Requests', href: '/driver', icon: Navigation, roles: ['driver'] },
  { name: 'Users', href: '/users', icon: Users, roles: ['admin', 'office_manager'] },
  { name: 'Drivers', href: '/drivers', icon: Car, roles: ['admin', 'office_manager'] },
  { name: 'Orders', href: '/orders', icon: FileText, roles: ['admin', 'office_manager', 'driver'] },
  { name: 'Payments', href: '/payments', icon: CreditCard, roles: ['admin', 'office_manager'] },
  { name: 'Analytics', href: '/analytics', icon: BarChart3, roles: ['admin', 'office_manager'] },
  { name: 'Settings', href: '/settings', icon: Settings },
]

export function DashboardSidebar() {
  const { userProfile } = useAuth()
  const pathname = usePathname()
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)

  if (!userProfile) {
    return null
  }
  const isPrivileged = userProfile.roles.some(r => ['admin', 'office_manager', 'support'].includes(r))
  const filteredNavigation = navigation.filter(item => {
    if (!item.roles) return true
    return item.roles.some(role => userProfile.roles.includes(role))
  }).filter(item => {
    // Hide the global Dashboard item for customer/driver-only users
    if (!isPrivileged && item.name === 'Dashboard') return false
    return true
  })

  return (
    <>
      {/* Mobile Menu Button */}
      <button
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
        className="md:hidden fixed top-4 left-4 z-40 p-3 glass-effect rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
      >
        {isMobileMenuOpen ? (
          <X className="w-6 h-6 text-gray-700" />
        ) : (
          <Menu className="w-6 h-6 text-gray-700" />
        )}
      </button>

      {/* Mobile Overlay */}
      {isMobileMenuOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black/50 z-30"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Desktop Sidebar */}
      <aside className="hidden md:flex opacity-0 animate-fade-in-left fixed z-30 inset-y-0 left-0 w-20 glass-effect flex-col transition-all duration-500 hover:shadow-2xl hover:w-24 border-r pt-6 pb-6 shadow-2xl items-center">
        <div className="flex flex-col space-y-8">
          {/* Yellow Taxi Logo */}
          <div className="transition-all duration-300 cursor-pointer hover:text-yellow-600 animate-glow text-yellow-500">
            <div className="w-10 h-10 rounded-xl flex items-center justify-center font-bold text-sm tracking-tight bg-gradient-to-br from-yellow-500 to-orange-500 text-black shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
              <Logo size="sm" showText={false} />
            </div>
          </div>

          {/* Navigation Items */}
          {filteredNavigation.slice(0, -1).map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'p-3 rounded-xl transition-all duration-300 group transform hover:scale-110',
                  isActive
                    ? 'text-yellow-600 bg-white/80 shadow-lg backdrop-blur-sm scale-110'
                    : 'text-gray-400 hover:text-yellow-600 hover:bg-white/50 hover:shadow-lg'
                )}
              >
                <item.icon className="w-5 h-5" strokeWidth={1.5} />
              </Link>
            )
          })}
        </div>

        {/* Settings at Bottom */}
        <div className="mt-auto">
          {filteredNavigation.slice(-1).map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                className={cn(
                  'p-3 rounded-xl transition-all duration-300 group transform hover:scale-110',
                  isActive
                    ? 'text-yellow-600 bg-white/80 shadow-lg backdrop-blur-sm scale-110'
                    : 'text-gray-400 hover:text-yellow-600 hover:bg-white/50 hover:shadow-lg'
                )}
              >
                <item.icon className="w-5 h-5" strokeWidth={1.5} />
              </Link>
            )
          })}
        </div>
      </aside>

      {/* Mobile Sidebar */}
      <aside className={cn(
        "md:hidden fixed z-40 inset-y-0 left-0 w-80 glass-effect flex flex-col transition-all duration-300 border-r shadow-2xl",
        isMobileMenuOpen ? "translate-x-0" : "-translate-x-full"
      )}>
        <div className="p-6 border-b border-white/20">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-xl flex items-center justify-center font-bold text-sm tracking-tight bg-gradient-to-br from-yellow-500 to-orange-500 text-black shadow-lg">
                <Logo size="sm" showText={false} />
              </div>
              <div>
                <h2 className="text-lg font-semibold text-gray-900">Yellow Taxi</h2>
                <p className="text-sm text-gray-500">Dashboard</p>
              </div>
            </div>
            <button
              onClick={() => setIsMobileMenuOpen(false)}
              className="p-2 rounded-lg hover:bg-white/50 transition-colors"
            >
              <X className="w-5 h-5 text-gray-500" />
            </button>
          </div>
        </div>

        <div className="flex-1 px-6 py-6 space-y-2">
          {filteredNavigation.map((item) => {
            const isActive = pathname === item.href
            return (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setIsMobileMenuOpen(false)}
                className={cn(
                  'flex items-center space-x-3 px-4 py-3 rounded-xl transition-all duration-300 group',
                  isActive
                    ? 'text-yellow-600 bg-white/80 shadow-lg backdrop-blur-sm'
                    : 'text-gray-600 hover:text-yellow-600 hover:bg-white/50'
                )}
              >
                <item.icon className="w-5 h-5" strokeWidth={1.5} />
                <span className="font-medium">{item.name}</span>
              </Link>
            )
          })}
        </div>
      </aside>
    </>
  )
}