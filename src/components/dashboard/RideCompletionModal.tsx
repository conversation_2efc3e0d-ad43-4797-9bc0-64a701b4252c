'use client'

import { useState } from 'react'
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON><PERSON>eader, DialogTitle, DialogFooter } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { OrderDocument } from '@/types/database'
import { Clock, MapPin, Car, User, CreditCard } from 'lucide-react'
import { formatCurrency } from '@/lib/utils'

interface RideCompletionModalProps {
  order: OrderDocument | null
  open: boolean
  onClose: () => void
}

export function RideCompletionModal({ order, open, onClose }: RideCompletionModalProps) {
  if (!order) return null

  // Calculate ride duration if available
  const calculateDuration = () => {
    const pickedUpEvent = order.status.timeline.find(event => event.status === 'picked_up')
    const completedEvent = order.status.timeline.find(event => event.status === 'completed')
    
    if (pickedUpEvent?.timestamp && completedEvent?.timestamp) {
      const startTime = pickedUpEvent.timestamp.toDate()
      const endTime = completedEvent.timestamp.toDate()
      const durationMs = endTime.getTime() - startTime.getTime()
      const minutes = Math.round(durationMs / 60000)
      return `${minutes} minutes`
    }
    
    return 'N/A'
  }

  return (
    <Dialog open={open} onOpenChange={(open) => !open && onClose()}>
      <DialogContent className="max-w-3xl">
        <DialogHeader>
          <DialogTitle className="text-2xl font-bold text-center text-yellow-600">
            Ride Completed
          </DialogTitle>
          <p className="text-center text-gray-600 mt-2">
            Thank you for riding with Yellow Taxi!
          </p>
        </DialogHeader>

        <div className="mt-6 space-y-6">
          {/* Trip Summary */}
          <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
            <h3 className="font-semibold text-gray-900 mb-3">Trip Summary</h3>
            
            <div className="space-y-4">
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-yellow-600 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-600">From</p>
                  <p className="font-medium text-gray-900">{order.locations.pickup.address}</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <MapPin className="w-5 h-5 text-red-600 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-600">To</p>
                  <p className="font-medium text-gray-900">{order.locations.destination.address}</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Clock className="w-5 h-5 text-blue-600 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-600">Duration</p>
                  <p className="font-medium text-gray-900">{calculateDuration()}</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <Car className="w-5 h-5 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm text-gray-600">Distance</p>
                  <p className="font-medium text-gray-900">{order.locations.route?.distance?.toFixed(1) || 'N/A'} km</p>
                </div>
              </div>
            </div>
          </div>
          
          {/* Driver Info */}
          {order.driver && (
            <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
              <h3 className="font-semibold text-gray-900 mb-3">Driver</h3>
              
              <div className="flex items-center space-x-3">
                {order.driver.avatar ? (
                  <img 
                    src={order.driver.avatar} 
                    alt={order.driver.name} 
                    className="w-12 h-12 rounded-full object-cover"
                  />
                ) : (
                  <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center">
                    <User className="w-6 h-6 text-gray-500" />
                  </div>
                )}
                
                <div>
                  <p className="font-medium text-gray-900">{order.driver.name}</p>
                  <p className="text-sm text-gray-600">{order.driver.phone}</p>
                </div>
              </div>
            </div>
          )}
          
          {/* Payment Info */}
          <div className="bg-gray-50 p-4 rounded-lg border border-gray-200">
            <h3 className="font-semibold text-gray-900 mb-3">Payment</h3>
            
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Base fare</span>
                <span className="font-medium">{formatCurrency(order.pricing.baseFare, 'JOD')}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Distance ({order.locations.route?.distance?.toFixed(1) || 0} km)</span>
                <span className="font-medium">{formatCurrency(order.pricing.distanceFare, 'JOD')}</span>
              </div>
              
              <div className="flex justify-between text-sm">
                <span className="text-gray-600">Time</span>
                <span className="font-medium">{formatCurrency(order.pricing.timeFare, 'JOD')}</span>
              </div>
              
              {order.pricing.surcharge > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Surcharge</span>
                  <span className="font-medium">{formatCurrency(order.pricing.surcharge, 'JOD')}</span>
                </div>
              )}
              
              {order.pricing.discount > 0 && (
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Discount</span>
                  <span className="font-medium text-green-600">-{formatCurrency(order.pricing.discount, 'JOD')}</span>
                </div>
              )}
              
              <div className="border-t pt-2 mt-2">
                <div className="flex justify-between font-semibold">
                  <span>Total</span>
                  <span className="text-yellow-600">{formatCurrency(order.pricing.total, 'JOD')}</span>
                </div>
                
                <div className="flex items-center mt-2 text-sm text-gray-600">
                  <CreditCard className="w-4 h-4 mr-1" />
                  <span>Paid with {order.payment.method === 'cash' ? 'Cash' : order.payment.method === 'card' ? 'Card' : 'Wallet'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <DialogFooter className="mt-6">
          <Button 
            onClick={onClose}
            className="w-full bg-gradient-to-r from-yellow-500 to-yellow-600 hover:from-yellow-600 hover:to-yellow-700 text-black font-semibold py-2 px-4 rounded-lg transition-all duration-300"
          >
            Close
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}