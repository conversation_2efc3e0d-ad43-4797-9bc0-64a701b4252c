import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Smartphone, Play, Star, Users } from "lucide-react";

export function HeroSection() {
  return (
    <section className="relative py-20 lg:py-32 overflow-hidden">
      {/* Background Image with Overlay */}
      <div className="absolute inset-0 z-0">
        <div 
          className="w-full h-full bg-cover bg-center bg-no-repeat"
          style={{
            backgroundImage: "url('/amman-view.jpg')"
          }}
        />
        <div className="absolute inset-0 bg-black/40" /> {/* Dark overlay for better text readability */}
      </div>
      
      <div className="container mx-auto px-4 relative z-10">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          <div className="space-y-8">
            <div className="space-y-4">
              <Badge className="bg-yellow-400 text-black border-yellow-300 font-semibold">
                Now Available in Amman
              </Badge>
              <h1 className="text-4xl lg:text-6xl font-bold text-white leading-tight">
                Your Ride,
                <span className="text-yellow-400"> Your Way</span>
              </h1>
              <p className="text-xl text-gray-200 max-w-lg">
                Safe, reliable, and affordable taxi rides at your fingertips. 
                Book instantly or schedule ahead with Jordan&apos;s most trusted ride service.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4">
              <Button size="lg" className="bg-yellow-400 hover:bg-yellow-500 text-black text-lg px-8" asChild>
                <Link href="/register">
                  <Smartphone className="mr-2 h-5 w-5" />
                  Book a Ride Now
                </Link>
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8">
                <Play className="mr-2 h-4 w-4" />
                Watch Demo
              </Button>
            </div>

            <div className="flex items-center space-x-8 pt-4">
              <div className="flex items-center space-x-2">
                <Star className="h-5 w-5 text-yellow-400 fill-current" />
                <span className="text-sm text-white">4.8/5 Rating</span>
              </div>
              <div className="flex items-center space-x-2">
                <Users className="h-5 w-5 text-yellow-400" />
                <span className="text-sm text-white">2000+ Happy Customers</span>
              </div>
            </div>
          </div>

          <div className="relative">
            <div className="relative w-full h-96 lg:h-[500px] rounded-2xl overflow-hidden bg-white/10 backdrop-blur-sm border border-white/20 p-8">
              {/* Mock Phone Interface */}
              <div className="absolute inset-4 bg-gray-900 rounded-xl flex items-center justify-center">
                <div className="w-72 h-80 bg-white rounded-lg shadow-2xl p-4 relative">
                  <div className="h-full flex flex-col">
                    <div className="flex items-center justify-between mb-4">
                      <h3 className="font-semibold text-gray-900">Book Your Ride</h3>
                      <div className="w-3 h-3 bg-green-400 rounded-full"></div>
                    </div>
                    
                    <div className="space-y-3 flex-1">
                      <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">King Hussein Street</span>
                      </div>
                      <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-sm text-gray-600">Rainbow Street</span>
                      </div>
                    </div>
                    
                    <div className="bg-yellow-400 rounded-lg p-3 text-center">
                      <span className="font-semibold text-black">5.5 JOD</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
