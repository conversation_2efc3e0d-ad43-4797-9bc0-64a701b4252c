'use client'

import { useEffect, useRef, useState } from 'react'
import { Car, MapPin, Clock, Star } from 'lucide-react'
import { useGoogleMaps, GoogleMapsUtils } from '@/lib/googleMaps'
import { DriverDocument } from '@/types/database'

interface Location {
  lat: number
  lng: number
}

interface NearbyDriver extends DriverDocument {
  distance: number
}

interface NearbyDriversMapProps {
  pickupLocation: Location
  drivers: NearbyDriver[]
  className?: string
  showDriverInfo?: boolean
}

export function NearbyDriversMap({
  pickupLocation,
  drivers,
  className = '',
  showDriverInfo = true
}: NearbyDriversMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const infoWindowRef = useRef<google.maps.InfoWindow | null>(null)

  // Use the Google Maps hook
  const { isLoaded, isLoading, error } = useGoogleMaps()

  // Create map once when Google Maps has loaded
  useEffect(() => {
    if (!isLoaded || !mapRef.current || map) return

    // Check if Google Maps API is available
    if (typeof window !== 'undefined' && window.google && window.google.maps) {
      // Initialize real Google Maps
      const mapOptions: google.maps.MapOptions = {
        center: pickupLocation,
        zoom: 14,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        disableDefaultUI: false,
        zoomControl: true,
        streetViewControl: false,
        fullscreenControl: false,
        styles: [
          {
            featureType: 'poi',
            elementType: 'labels',
            stylers: [{ visibility: 'off' }]
          }
        ]
      }

      const googleMap = new google.maps.Map(mapRef.current, mapOptions)
      setMap(googleMap)
    }
  }, [isLoaded, map, pickupLocation])

  // Add markers for pickup location and drivers
  useEffect(() => {
    if (!map || !window.google?.maps) return

    // Clear existing markers
    markersRef.current.forEach(marker => marker.setMap(null))
    markersRef.current = []

    // Add pickup location marker (person icon)
    const pickupMarker = new google.maps.Marker({
      position: pickupLocation,
      map: map,
      title: 'Pickup Location',
      icon: {
        url: '/passenger.png',
        scaledSize: new google.maps.Size(32, 32),
        anchor: new google.maps.Point(16, 16)
      }
    })
    markersRef.current.push(pickupMarker)

    // Add driver markers
    drivers.forEach((driver, index) => {
      const driverPosition = {
        lat: driver.location.current.latitude,
        lng: driver.location.current.longitude
      }

      // Create a static taxi icon that matches the driver map style
      const createStaticTaxiIcon = () => {
        const svgIcon = `
          <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
            <!-- Taxi icon scaled and centered from the original taxi.svg -->
            <g transform="translate(40, 40) scale(0.2) translate(-43.85, -93.13)">
              <!-- Main taxi body from taxi.svg -->
              <path d="M 43.278274,0.5013013 36.191219,0.4540543 33.302731,0.70405167 31.653722,0.93032775 30.001003,1.19945 l -1.937702,0.4677216 -1.403165,0.3340869 -2.07134,0.4677217 -1.937704,0.6681738 -2.004521,0.7349913 -1.670435,0.734991 -2.204973,1.0690781 -1.603617,0.868626 -1.628674,1.1526 -1.779013,1.3864604 -1.469982,1.5368002 -1.002261,1.403165 -1.2027128,1.870886 -0.5345392,1.202713 -0.5345391,1.603617 3.704e-4,1.9667 V 154.17784 l 2.62221,23.71241 0.3173968,1.21048 0.604,0.91564 0.969,1.228 0.898,0.709 0.992,0.661 1.039,0.614 1.039,0.425 1.370,0.567 1.323,0.472 1.311,0.437 1.382,0.366 1.228,0.343 1.559,0.319 1.536,0.177 1.394,0.106 h 32.750466 l 2.961,-0.376 3.036,-0.484 2.706,-0.702 2.238,-0.668 2.071,-0.869 1.215,-0.727 0.990,-0.743 1.019,-1.186 0.651,-0.902 0.585,-1.069 0.635,-4.694 1.620,-12.512 0.451,-4.276 V 18.243 L 79.328,16.991 78.879,15.549 78.336,14.392 77.650,13.187 76.611,11.510 75.631,10.317 74.650,9.396 73.942,8.710 73.103,8.037 72.323,7.482 71.249,6.785 70.133,6.027 69.014,5.426 67.803,4.849 66.258,4.190 64.679,3.538 63.159,2.953 61.685,2.423 60.286,1.985 58.799,1.567 56.862,1.149 54.406,0.715 50.294,0.501 Z" fill="#ffd000" stroke="#000000" stroke-width="0.5"/>

              <!-- Windows from taxi.svg -->
              <path d="m 24.290141,61.82597 -3.315627,0.356523 -3.671106,0.398039 -2.538713,0.393146 -1.311105,0.386835 -0.425223,0.221471 -0.318918,0.206705 -0.262811,0.197846 -0.268718,0.212613 -0.230329,0.256905 -0.277576,0.419316 -0.165365,0.472472 -0.02362,0.614212 0.02362,2.031622 0.4016,1.27567 5.98856,14.008742 0.377976,0.791388 0.413412,0.626022 0.885882,0.73233 0.555152,0.236233 1.724516,0.02363 3.09468,-0.519717 3.803385,-0.472472 5.433409,-0.401598 5.120569,-0.19472 7.936097,-0.01277 8.586033,0.467723 4.359836,0.334086 4.802523,0.634101 0.850448,0.165364 1.275667,-0.354353 0.519719,-0.47247 0.803201,-0.826822 0.74414,-1.559153 0.838634,-1.889882 4.748326,-11.551896 0.307105,-0.803198 -0.02363,-2.456847 -0.04725,-0.54334 L 74.650289,64.473777 74.31956,64.190295 73.941584,63.717823 73.209255,63.363472 71.862713,62.891 69.030698,62.395823 65.464321,61.978215 60.686877,61.619073 52.46834,61.318394 l -11.906779,0.03708 -11.61294,0.17131 z" fill="#585d59" stroke="#000000" stroke-width="0.3"/>

              <!-- Taxi roof sign from taxi.svg -->
              <path d="M 29.018,8.068 C 38.509,6.957 48.000,6.902 57.492,7.892 V 4.214 c -9.491,-0.876 -19.081,-0.876 -28.474,0 l 0.000,3.854 z" fill="#595959" stroke="#000000" stroke-width="0.2"/>

              <!-- Headlights from taxi.svg -->
              <path d="m 24.705727,4.7908839 0.542891,0.058465 0.425961,0.1586912 0.471898,0.3758477 0.263093,0.2422132 0.250566,0.4677216 -0.0167,1.1860086 -0.283974,0.534539 -0.952147,0.4844259 -0.818513,0.334087 -0.622744,0.3492584 -0.744141,0.4134115 -0.885881,0.4015996 -0.696894,0.4016004 -0.755952,0.460658 -0.838635,0.425223 -0.968564,0.6024 -0.862258,0.637835 -0.614211,0.377976 -0.696894,0.519717 -0.602399,0.425223 -0.826823,0.685082 -0.815011,0.649647 -0.708706,0.779576 c -0.261247,0.268734 -0.523122,0.237501 -2.657643,0.224381 v 0 l -1.4701435,-0.02708 0.025314,-0.895373 0.2171565,-0.943796 0.233861,-0.567949 0.50113,-0.7684 0.621176,-0.777121 0.553842,-0.590711 0.534273,-0.508622 0.933128,-0.732329 0.862258,-0.6496468 0.850447,-0.6023994 0.956752,-0.578776 0.897693,-0.5197173 1.252047,-0.7087053 1.024192,-0.5292836 0.997368,-0.4133096 0.815053,-0.3383859 0.855382,-0.3277615 1.249366,-0.423934 0.88533,-0.2004523 0.417609,-0.083522 z" fill="#deffff" stroke="#000000" stroke-width="0.1"/>

              <!-- Mirror headlight on right side -->
              <g transform="matrix(-1,0,0,1,86.637266,-0.03390042)">
                <path d="m 24.705727,4.7908839 0.542891,0.058465 0.425961,0.1586912 0.471898,0.3758477 0.263093,0.2422132 0.250566,0.4677216 -0.0167,1.1860086 -0.283974,0.534539 -0.952147,0.4844259 -0.818513,0.334087 -0.622744,0.3492584 -0.744141,0.4134115 -0.885881,0.4015996 -0.696894,0.4016004 -0.755952,0.460658 -0.838635,0.425223 -0.968564,0.6024 -0.862258,0.637835 -0.614211,0.377976 -0.696894,0.519717 -0.602399,0.425223 -0.826823,0.685082 -0.815011,0.649647 -0.708706,0.779576 c -0.261247,0.268734 -0.523122,0.237501 -2.657643,0.224381 v 0 l -1.4701435,-0.02708 0.025314,-0.895373 0.2171565,-0.943796 0.233861,-0.567949 0.50113,-0.7684 0.621176,-0.777121 0.553842,-0.590711 0.534273,-0.508622 0.933128,-0.732329 0.862258,-0.6496468 0.850447,-0.6023994 0.956752,-0.578776 0.897693,-0.5197173 1.252047,-0.7087053 1.024192,-0.5292836 0.997368,-0.4133096 0.815053,-0.3383859 0.855382,-0.3277615 1.249366,-0.423934 0.88533,-0.2004523 0.417609,-0.083522 z" fill="#deffff" stroke="#000000" stroke-width="0.1"/>
              </g>
            </g>
          </svg>
        `
        return 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svgIcon)
      }

      const driverMarker = new google.maps.Marker({
        position: driverPosition,
        map: map,
        title: `${driver.personalInfo.firstName} ${driver.personalInfo.lastName}`,
        icon: {
          url: createStaticTaxiIcon(),
          scaledSize: new google.maps.Size(80, 80),
          anchor: new google.maps.Point(40, 40)
        }
      })

      // Add click listener for driver info
      if (showDriverInfo) {
        driverMarker.addListener('click', () => {
          if (infoWindowRef.current) {
            infoWindowRef.current.close()
          }

          const infoWindow = new google.maps.InfoWindow({
            content: `
              <div class="p-3 min-w-[200px]">
                <div class="flex items-center space-x-2 mb-2">
                  <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                    <span class="text-white text-xs font-bold">${driver.personalInfo.firstName[0]}</span>
                  </div>
                  <div>
                    <h3 class="font-semibold text-gray-900">${driver.personalInfo.firstName} ${driver.personalInfo.lastName}</h3>
                    <p class="text-sm text-gray-600">${driver.vehicle.make} ${driver.vehicle.model}</p>
                  </div>
                </div>
                <div class="space-y-1 text-sm">
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">Distance:</span>
                    <span class="font-medium">${driver.distance.toFixed(1)} km</span>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">Rating:</span>
                    <div class="flex items-center">
                      <span class="font-medium">${driver.earnings.rating.average.toFixed(1)}</span>
                      <span class="text-yellow-500 ml-1">★</span>
                    </div>
                  </div>
                  <div class="flex items-center justify-between">
                    <span class="text-gray-600">Trips:</span>
                    <span class="font-medium">${driver.earnings.completedTrips}</span>
                  </div>
                </div>
              </div>
            `
          })

          infoWindow.open(map, driverMarker)
          infoWindowRef.current = infoWindow
        })
      }

      markersRef.current.push(driverMarker)
    })

    // Adjust map bounds to show all markers
    if (drivers.length > 0) {
      const bounds = new google.maps.LatLngBounds()
      bounds.extend(pickupLocation)
      
      drivers.forEach(driver => {
        bounds.extend({
          lat: driver.location.current.latitude,
          lng: driver.location.current.longitude
        })
      })

      map.fitBounds(bounds)
      
      // Ensure minimum zoom level
      const listener = google.maps.event.addListener(map, 'bounds_changed', () => {
        if (map.getZoom() && map.getZoom()! > 16) {
          map.setZoom(16)
        }
        google.maps.event.removeListener(listener)
      })
    }
  }, [map, pickupLocation, drivers, showDriverInfo])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      markersRef.current.forEach(marker => marker.setMap(null))
      if (infoWindowRef.current) {
        infoWindowRef.current.close()
      }
    }
  }, [])

  if (isLoading) {
    return (
      <div className={`relative bg-gray-100 rounded-xl overflow-hidden ${className}`}>
        <div className="flex items-center justify-center h-full min-h-[300px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-500 mx-auto mb-2"></div>
            <p className="text-gray-600">Loading map...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className={`relative bg-gray-100 rounded-xl overflow-hidden ${className}`}>
        <div className="flex items-center justify-center h-full min-h-[300px]">
          <div className="text-center">
            <MapPin className="w-8 h-8 text-gray-400 mx-auto mb-2" />
            <p className="text-gray-600">Map unavailable</p>
            <p className="text-sm text-gray-500">{error}</p>
          </div>
        </div>
      </div>
    )
  }

  // Render the map container
  return (
    <div className={`relative bg-gray-100 rounded-xl overflow-hidden ${className}`}>
      <div ref={mapRef} className="w-full h-full min-h-[300px]">
        {/* Fallback demo map if Google Maps fails to load */}
        {!isLoaded && (
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
            <div className="absolute inset-0 opacity-20">
              <svg width="100%" height="100%" className="w-full h-full">
                <defs>
                  <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                    <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#94a3b8" strokeWidth="1"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />
              </svg>
            </div>
            
            {/* Demo markers */}
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="w-6 h-6 bg-green-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                <img src="/passenger.png" alt="Passenger" className="w-3 h-3" />
              </div>
              <p className="text-xs text-gray-700 mt-1 text-center">Pickup</p>
            </div>
            
            {drivers.slice(0, 3).map((driver, index) => (
              <div
                key={driver.id}
                className="absolute"
                style={{
                  top: `${40 + index * 15}%`,
                  left: `${30 + index * 20}%`
                }}
              >
                <div className="w-8 h-8 bg-yellow-500 rounded-full border-2 border-white shadow-lg flex items-center justify-center">
                  <img src="/taxi.svg" alt="Taxi" className="w-5 h-5" />
                </div>
                <p className="text-xs text-gray-700 mt-1 text-center">{driver.personalInfo.firstName}</p>
              </div>
            ))}
          </div>
        )}
      </div>
      
      {/* Driver count overlay */}
      {drivers.length > 0 && (
        <div className="absolute top-4 right-4 bg-white/90 backdrop-blur-sm rounded-lg px-3 py-2 shadow-lg">
          <div className="flex items-center space-x-2">
            <Car className="w-4 h-4 text-yellow-500" />
            <span className="text-sm font-medium text-gray-900">
              {drivers.length} driver{drivers.length !== 1 ? 's' : ''} nearby
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

export default NearbyDriversMap
