'use client'

import { useEffect, useRef, useState } from 'react'
import { MapPin, Navigation, Car } from 'lucide-react'
import { useGoogleMaps, GoogleMapsUtils } from '@/lib/googleMaps'

interface Location {
  lat: number
  lng: number
}

interface RideTrackingMapProps {
  pickup: Location
  destination: Location
  driverLocation?: Location
  driverBearing?: number
  showRoute?: boolean
  className?: string
}

export function RideTrackingMap({
  pickup,
  destination,
  driverLocation,
  driverBearing = 0,
  showRoute = true,
  className = ''
}: RideTrackingMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const [map, setMap] = useState<google.maps.Map | null>(null)
  const markersRef = useRef<google.maps.Marker[]>([])
  const driverMarkerRef = useRef<google.maps.Marker | null>(null)
  const routeRef = useRef<google.maps.DirectionsRenderer | null>(null)

  // Use the Google Maps hook
  const { isLoaded, isLoading, error } = useGoogleMaps()

  // Google Maps is loaded via the useGoogleMaps hook

  // Create map once when Google Maps has loaded
  useEffect(() => {
    if (!isLoaded || !mapRef.current || map) return

    // Check if Google Maps API is available
    if (typeof window !== 'undefined' && window.google && window.google.maps) {
      // Initialize real Google Maps
      const mapOptions: google.maps.MapOptions = {
        center: pickup,
        zoom: 13,
        mapTypeId: google.maps.MapTypeId.ROADMAP,
        disableDefaultUI: false,
        zoomControl: true,
        streetViewControl: false,
        fullscreenControl: false,
      }

      const googleMap = new google.maps.Map(mapRef.current, mapOptions)
      setMap(googleMap)
    } else {
      // Fallback to demo mode
      const mockMap = {
        setCenter: () => {},
        setZoom: () => {},
        addMarker: () => {},
        drawRoute: () => {}
      }
      setMap(mockMap as any)
    }
  }, [isLoaded, map])

  // Update pickup/destination markers and route when they change
  useEffect(() => {
    if (!map || !window.google) return

    // Clear existing pickup/destination markers
    markersRef.current.forEach(marker => {
      marker.setMap(null)
    })
    markersRef.current = []

    // Add pickup marker (person icon)
    const pickupMarker = new google.maps.Marker({
      position: pickup,
      map: map,
      title: 'Pickup Location',
      icon: {
        url: '/passenger.png',
        scaledSize: new google.maps.Size(32, 32),
        anchor: new google.maps.Point(16, 16)
      }
    })
    markersRef.current.push(pickupMarker)

    // Add destination marker
    const destinationMarker = new google.maps.Marker({
      position: destination,
      map: map,
      title: 'Destination',
      icon: {
        url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
          <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
            <circle cx="16" cy="16" r="12" fill="#EF4444" stroke="white" stroke-width="2"/>
            <path d="M12 16L14 18L20 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
          </svg>
        `),
        scaledSize: new google.maps.Size(32, 32),
        anchor: new google.maps.Point(16, 16)
      }
    })
    markersRef.current.push(destinationMarker)

    // Fit map to show pickup and destination
    const bounds = new google.maps.LatLngBounds()
    bounds.extend(pickup)
    bounds.extend(destination)
    if (driverLocation) {
      bounds.extend(driverLocation)
    }
    map.fitBounds(bounds)

    // Draw route if requested
    if (showRoute && routeRef.current) {
      routeRef.current.setMap(null)
    }

    if (showRoute) {
      const directionsService = new google.maps.DirectionsService()
      const directionsRenderer = new google.maps.DirectionsRenderer({
        suppressMarkers: true, // We're using custom markers
        polylineOptions: {
          strokeColor: '#3B82F6',
          strokeWeight: 4,
          strokeOpacity: 0.8
        }
      })

      directionsService.route({
        origin: pickup,
        destination: destination,
        travelMode: google.maps.TravelMode.DRIVING
      }, (result, status) => {
        if (status === google.maps.DirectionsStatus.OK && result) {
          directionsRenderer.setDirections(result)
          directionsRenderer.setMap(map)
          routeRef.current = directionsRenderer
        }
      })
    }
  }, [map, pickup, destination, showRoute])

  // Update driver marker separately to avoid recreating pickup/destination markers
  useEffect(() => {
    if (!map || !window.google) return

    // Remove existing driver marker
    if (driverMarkerRef.current) {
      driverMarkerRef.current.setMap(null)
      driverMarkerRef.current = null
    }

    // Add driver marker if available (taxi icon)
    if (driverLocation) {
      // Create a rotated taxi SVG icon using the actual taxi.svg with direction indicator
      const createRotatedTaxiIcon = (bearing: number) => {
        // Use the actual taxi.svg content with rotation and a red direction marker
        const svgIcon = `
          <svg width="80" height="80" viewBox="0 0 80 80" xmlns="http://www.w3.org/2000/svg">
            <g transform="rotate(${bearing} 40 40)">
              <!-- Taxi icon scaled and centered from the original taxi.svg -->
              <g transform="translate(40, 40) scale(0.2) translate(-43.85, -93.13)">
                <!-- Main taxi body from taxi.svg -->
                <path d="M 43.278274,0.5013013 36.191219,0.4540543 33.302731,0.70405167 31.653722,0.93032775 30.001003,1.19945 l -1.937702,0.4677216 -1.403165,0.3340869 -2.07134,0.4677217 -1.937704,0.6681738 -2.004521,0.7349913 -1.670435,0.734991 -2.204973,1.0690781 -1.603617,0.868626 -1.628674,1.1526 -1.779013,1.3864604 -1.469982,1.5368002 -1.002261,1.403165 -1.2027128,1.870886 -0.5345392,1.202713 -0.5345391,1.603617 3.704e-4,1.9667 V 154.17784 l 2.62221,23.71241 0.3173968,1.21048 0.604,0.91564 0.969,1.228 0.898,0.709 0.992,0.661 1.039,0.614 1.039,0.425 1.370,0.567 1.323,0.472 1.311,0.437 1.382,0.366 1.228,0.343 1.559,0.319 1.536,0.177 1.394,0.106 h 32.750466 l 2.961,-0.376 3.036,-0.484 2.706,-0.702 2.238,-0.668 2.071,-0.869 1.215,-0.727 0.990,-0.743 1.019,-1.186 0.651,-0.902 0.585,-1.069 0.635,-4.694 1.620,-12.512 0.451,-4.276 V 18.243 L 79.328,16.991 78.879,15.549 78.336,14.392 77.650,13.187 76.611,11.510 75.631,10.317 74.650,9.396 73.942,8.710 73.103,8.037 72.323,7.482 71.249,6.785 70.133,6.027 69.014,5.426 67.803,4.849 66.258,4.190 64.679,3.538 63.159,2.953 61.685,2.423 60.286,1.985 58.799,1.567 56.862,1.149 54.406,0.715 50.294,0.501 Z" fill="#ffd000" stroke="#000000" stroke-width="0.5"/>

                <!-- Windows from taxi.svg -->
                <path d="m 24.290141,61.82597 -3.315627,0.356523 -3.671106,0.398039 -2.538713,0.393146 -1.311105,0.386835 -0.425223,0.221471 -0.318918,0.206705 -0.262811,0.197846 -0.268718,0.212613 -0.230329,0.256905 -0.277576,0.419316 -0.165365,0.472472 -0.02362,0.614212 0.02362,2.031622 0.4016,1.27567 5.98856,14.008742 0.377976,0.791388 0.413412,0.626022 0.885882,0.73233 0.555152,0.236233 1.724516,0.02363 3.09468,-0.519717 3.803385,-0.472472 5.433409,-0.401598 5.120569,-0.19472 7.936097,-0.01277 8.586033,0.467723 4.359836,0.334086 4.802523,0.634101 0.850448,0.165364 1.275667,-0.354353 0.519719,-0.47247 0.803201,-0.826822 0.74414,-1.559153 0.838634,-1.889882 4.748326,-11.551896 0.307105,-0.803198 -0.02363,-2.456847 -0.04725,-0.54334 L 74.650289,64.473777 74.31956,64.190295 73.941584,63.717823 73.209255,63.363472 71.862713,62.891 69.030698,62.395823 65.464321,61.978215 60.686877,61.619073 52.46834,61.318394 l -11.906779,0.03708 -11.61294,0.17131 z" fill="#585d59" stroke="#000000" stroke-width="0.3"/>

                <!-- Taxi roof sign from taxi.svg -->
                <path d="M 29.018,8.068 C 38.509,6.957 48.000,6.902 57.492,7.892 V 4.214 c -9.491,-0.876 -19.081,-0.876 -28.474,0 l 0.000,3.854 z" fill="#595959" stroke="#000000" stroke-width="0.2"/>

                <!-- Headlights from taxi.svg -->
                <path d="m 24.705727,4.7908839 0.542891,0.058465 0.425961,0.1586912 0.471898,0.3758477 0.263093,0.2422132 0.250566,0.4677216 -0.0167,1.1860086 -0.283974,0.534539 -0.952147,0.4844259 -0.818513,0.334087 -0.622744,0.3492584 -0.744141,0.4134115 -0.885881,0.4015996 -0.696894,0.4016004 -0.755952,0.460658 -0.838635,0.425223 -0.968564,0.6024 -0.862258,0.637835 -0.614211,0.377976 -0.696894,0.519717 -0.602399,0.425223 -0.826823,0.685082 -0.815011,0.649647 -0.708706,0.779576 c -0.261247,0.268734 -0.523122,0.237501 -2.657643,0.224381 v 0 l -1.4701435,-0.02708 0.025314,-0.895373 0.2171565,-0.943796 0.233861,-0.567949 0.50113,-0.7684 0.621176,-0.777121 0.553842,-0.590711 0.534273,-0.508622 0.933128,-0.732329 0.862258,-0.6496468 0.850447,-0.6023994 0.956752,-0.578776 0.897693,-0.5197173 1.252047,-0.7087053 1.024192,-0.5292836 0.997368,-0.4133096 0.815053,-0.3383859 0.855382,-0.3277615 1.249366,-0.423934 0.88533,-0.2004523 0.417609,-0.083522 z" fill="#deffff" stroke="#000000" stroke-width="0.1"/>

                <!-- Mirror headlight on right side -->
                <g transform="matrix(-1,0,0,1,86.637266,-0.03390042)">
                  <path d="m 24.705727,4.7908839 0.542891,0.058465 0.425961,0.1586912 0.471898,0.3758477 0.263093,0.2422132 0.250566,0.4677216 -0.0167,1.1860086 -0.283974,0.534539 -0.952147,0.4844259 -0.818513,0.334087 -0.622744,0.3492584 -0.744141,0.4134115 -0.885881,0.4015996 -0.696894,0.4016004 -0.755952,0.460658 -0.838635,0.425223 -0.968564,0.6024 -0.862258,0.637835 -0.614211,0.377976 -0.696894,0.519717 -0.602399,0.425223 -0.826823,0.685082 -0.815011,0.649647 -0.708706,0.779576 c -0.261247,0.268734 -0.523122,0.237501 -2.657643,0.224381 v 0 l -1.4701435,-0.02708 0.025314,-0.895373 0.2171565,-0.943796 0.233861,-0.567949 0.50113,-0.7684 0.621176,-0.777121 0.553842,-0.590711 0.534273,-0.508622 0.933128,-0.732329 0.862258,-0.6496468 0.850447,-0.6023994 0.956752,-0.578776 0.897693,-0.5197173 1.252047,-0.7087053 1.024192,-0.5292836 0.997368,-0.4133096 0.815053,-0.3383859 0.855382,-0.3277615 1.249366,-0.423934 0.88533,-0.2004523 0.417609,-0.083522 z" fill="#deffff" stroke="#000000" stroke-width="0.1"/>
                </g>
              </g>

              <!-- Red direction indicator at the front of the taxi -->
              <circle cx="40" cy="12" r="5" fill="#FF0000" stroke="#FFFFFF" stroke-width="2"/>
              <circle cx="40" cy="12" r="2.5" fill="#FFFFFF"/>
            </g>
          </svg>
        `
        return 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(svgIcon)
      }

      const driverMarker = new google.maps.Marker({
        position: driverLocation,
        map: map,
        title: 'Driver Location',
        icon: {
          url: createRotatedTaxiIcon(driverBearing),
          scaledSize: new google.maps.Size(80, 80),
          anchor: new google.maps.Point(40, 40)
        }
      })
      driverMarkerRef.current = driverMarker
    }
  }, [map, driverLocation, driverBearing])

  // For demo purposes, render a simple visual map
  return (
    <div className={`relative bg-gray-100 rounded-xl overflow-hidden ${className}`}>
      <div ref={mapRef} className="w-full h-full min-h-[300px] relative">
        {/* Demo Map Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-green-100">
          {/* Grid pattern to simulate map */}
          <div className="absolute inset-0 opacity-20">
            <svg width="100%" height="100%" className="w-full h-full">
              <defs>
                <pattern id="grid" width="40" height="40" patternUnits="userSpaceOnUse">
                  <path d="M 40 0 L 0 0 0 40" fill="none" stroke="#94a3b8" strokeWidth="1"/>
                </pattern>
              </defs>
              <rect width="100%" height="100%" fill="url(#grid)" />
            </svg>
          </div>

          {/* Pickup Location (Person Icon) */}
          <div className="absolute top-1/4 left-1/4 transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-green-500 p-2 rounded-full shadow-lg">
              <img src="/passenger.png" alt="Passenger" className="w-6 h-6" />
            </div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
              <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium">
                Pickup
              </div>
            </div>
          </div>

          {/* Destination Location */}
          <div className="absolute bottom-1/4 right-1/4 transform translate-x-1/2 translate-y-1/2">
            <div className="bg-red-500 text-white p-2 rounded-full shadow-lg">
              <MapPin className="w-6 h-6" />
            </div>
            <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-1">
              <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium">
                Destination
              </div>
            </div>
          </div>

          {/* Driver Location (if available) - Car Icon */}
          {driverLocation && (
            <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
              <div className="bg-yellow-500 p-2 rounded-full shadow-lg animate-pulse">
                <img src="/taxi.svg" alt="Taxi" className="w-8 h-8" />
              </div>
              <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1">
                <div className="bg-white px-2 py-1 rounded shadow text-xs font-medium">
                  Driver
                </div>
              </div>
            </div>
          )}

          {/* Route Line (demo) */}
          {showRoute && (
            <svg className="absolute inset-0 w-full h-full pointer-events-none">
              <path
                d="M 25% 25% Q 50% 50% 75% 75%"
                stroke="#3b82f6"
                strokeWidth="3"
                fill="none"
                strokeDasharray="5,5"
                className="animate-pulse"
              />
            </svg>
          )}
        </div>

        {/* Map Controls */}
        <div className="absolute top-4 right-4 space-y-2">
          <button className="bg-white p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <Navigation className="w-5 h-5 text-gray-600" />
          </button>
          <button className="bg-white p-2 rounded-lg shadow-md hover:shadow-lg transition-shadow">
            <MapPin className="w-5 h-5 text-gray-600" />
          </button>
        </div>

        {/* Loading State */}
        {!isLoaded && (
          <div className="absolute inset-0 flex items-center justify-center bg-gray-100">
            <div className="text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-yellow-600 mx-auto"></div>
              <p className="mt-2 text-gray-600 text-sm">Loading map...</p>
            </div>
          </div>
        )}

        {/* API Status Notice */}
        <div className="absolute bottom-4 left-4 bg-white/90 backdrop-blur-sm px-3 py-2 rounded-lg shadow-md">
          <p className="text-xs text-gray-600">
            {isLoaded && window.google?.maps ? (
              '🗺️ Google Maps Active'
            ) : error ? (
              '⚠️ Maps API Error - Demo Mode'
            ) : isLoading ? (
              '⏳ Loading Google Maps...'
            ) : (
              '📍 Demo Mode - Add API key for real maps'
            )}
          </p>
        </div>
      </div>
    </div>
  )
}

// Hook for real-time location tracking
export function useLocationTracking(orderId?: string) {
  const [driverLocation, setDriverLocation] = useState<Location | null>(null)
  const [isTracking, setIsTracking] = useState(false)

  useEffect(() => {
    if (!orderId) return

    // In a real app, you'd subscribe to real-time location updates here
    // For demo, simulate driver movement
    setIsTracking(true)
    
    const interval = setInterval(() => {
      // Simulate driver movement
      setDriverLocation(prev => {
        if (!prev) {
          return { lat: 31.9539, lng: 35.9106 } // Start at pickup area
        }
        
        // Simulate movement towards destination
        return {
          lat: prev.lat + (Math.random() - 0.5) * 0.001,
          lng: prev.lng + (Math.random() - 0.5) * 0.001
        }
      })
    }, 3000) // Update every 3 seconds

    return () => {
      clearInterval(interval)
      setIsTracking(false)
    }
  }, [orderId])

  return { driverLocation, isTracking }
}

export default RideTrackingMap
