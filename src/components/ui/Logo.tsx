import React from 'react';
import { View, StyleSheet } from 'react-native';
import Svg, { Path, G, ClipPath, Defs } from 'react-native-svg';

interface LogoProps {
  size?: number;
  color?: string;
}

export const Logo: React.FC<LogoProps> = ({ 
  size = 80, 
  color = '#FFC100' 
}) => {
  return (
    <View style={[styles.container, { width: size, height: size }]}>
      <Svg
        width={size}
        height={size}
        viewBox="0 0 381 381"
        style={styles.svg}
      >
        <Defs>
          <ClipPath id="clipPath2">
            <Path
              d="M 0,1080 H 1080 V 0 H 0 Z"
              transform="translate(-496.91501,-715.32232)"
            />
          </ClipPath>
          <ClipPath id="clipPath4">
            <Path
              d="M 0,1080 H 1080 V 0 H 0 Z"
              transform="translate(-555.15721,-333.13091)"
            />
          </ClipPath>
          <ClipPath id="clipPath6">
            <Path
              d="M 0,1080 H 1080 V 0 H 0 Z"
              transform="translate(-603.98441,-460.59771)"
            />
          </ClipPath>
        </Defs>
        
        <G transform="matrix(0.26458333,0,0,0.26458334,1e-5,0)">
          <G>
            {/* Main taxi body - black */}
            <Path
              d="m 0,0 c -0.499,26.425 0.233,52.861 -1.037,79.294 -0.746,15.543 -0.544,15.866 -15.647,12.558 -14.674,-2.475 -26.84,-10.32 -38.578,-18.701 -5.83,-4.163 -9.988,-3.568 -14.692,1.327 -10.108,10.517 -20.23,21.03 -30.575,31.31 -6.592,6.55 -5.822,16.316 1.691,21.786 30.072,21.893 63.615,35.41 100.835,40.734 30.878,4.416 61.377,2.61 91.454,-4.996 12.438,-3.146 24.411,-8.128 36.59,-12.298 0.209,-0.072 0.419,-0.154 0.62,-0.243 20.173,-8.953 26.146,-13.066 44.683,-27.343 3.791,-2.92 4.164,-8.524 0.76,-11.887 -0.027,-0.027 -0.054,-0.054 -0.082,-0.081 -12.536,-12.344 -25.052,-24.72 -37.164,-37.477 -4.794,-5.049 -8.689,-4.424 -13.7,-0.752 -12.899,9.456 -27.354,15.871 -42.446,20.964 -12.005,4.052 -13.494,3.087 -13.494,-9.279 10e-4,-77.005 0.028,-154.011 0.015,-231.017 -0.001,-2.827 -0.088,-5.654 -0.192,-8.481 -0.143,-3.922 -3.135,-7.171 -7.037,-7.585 -8.871,-0.94 -17.831,0.404 -26.725,0.001 -8.831,-0.4 -17.72,0.332 -26.537,-0.208 -7.415,-0.453 -9.957,2.306 -10.015,9.638 -0.122,15.332 1.267,30.603 1.337,45.931"
              fill="#000000"
              transform="matrix(1.3333333,0,0,-1.3333333,662.55333,486.23693)"
              clipPath="url(#clipPath2)"
            />
            
            {/* Yellow taxi top part */}
            <Path
              d="M 0,0 C 14.663,12.729 29.277,25.458 46.823,41.157 L 49.737,43.77 17.143,42.436 15.654,41.1 C 6.157,32.711 -2.343,25.312 -10.564,18.157 l -4.875,-4.244 C -21.61,8.561 -27.525,3.404 -33.251,-1.598 c -2.173,1.987 -4.749,4.357 -7.78,7.143 -9.308,8.546 -22.472,20.617 -38.776,35.467 l -1.819,1.513 -0.371,0.012 c -9.796,0.273 -31.759,1.233 -31.759,1.233 l 3.207,-2.771 c 19.905,-18.061 36.17,-32.967 48.046,-43.85 l 5.611,-5.143 c 8.795,-8.064 13.867,-12.71 15.823,-14.406 l 7.792,-6.808 7.73,6.876 c 7.619,6.758 15.675,13.756 24.204,21.165 z"
              fill={color}
              transform="matrix(1.3333333,0,0,-1.3333333,740.2096,995.82547)"
              clipPath="url(#clipPath4)"
            />
            
            {/* Yellow taxi main body */}
            <Path
              d="m 0,0 c -17.546,-15.699 -32.16,-28.427 -46.823,-41.157 l -1.343,-1.167 c -8.529,-7.409 -16.585,-14.406 -24.205,-21.165 l -7.73,-6.875 -7.792,6.806 c -1.956,1.697 -7.027,6.343 -15.822,14.408 l -5.611,5.142 c -11.876,10.883 -28.141,25.789 -48.047,43.85 l -3.206,2.77 c 0,0 21.962,-0.96 31.758,-1.232 l 0.371,-0.012 1.82,-1.514 c 16.304,-14.849 29.468,-26.92 38.776,-35.466 3.031,-2.786 5.607,-5.157 7.78,-7.142 5.725,5.002 11.64,10.157 17.811,15.51 l 4.874,4.244 c 8.223,7.155 16.723,14.554 26.22,22.943 l 1.487,1.335 32.596,1.334 z m 103.914,320.226 c -2.794,3.892 -4.628,6.419 -9.352,1.447 -13.158,-13.845 -26.651,-27.397 -40.53,-40.516 -4.527,-4.28 -2.403,-6.839 -0.122,-10.589 61.302,-100.767 2.566,-226.645 -114.446,-241.632 -61.601,-7.891 -112.119,15.407 -148.501,66.842 -0.049,-0.577 -16.827,31.93 -17.015,31.817 -0.681,1.215 -1.385,2.21 -2.016,3.653 -16.363,47.979 -12.188,93.963 14.107,137.47 3.691,6.106 4.269,9.568 -1.459,14.803 -12.521,11.444 -24.58,23.463 -36.038,35.972 -5.645,6.162 -8.024,5.747 -12.854,-0.907 -63.719,-87.78 -55.33,-212.96 20.589,-290.409 30.807,-31.428 61.961,-62.515 93.122,-93.595 7.107,-0.294 16.327,-0.658 21.78,-0.811 l 0.371,-0.01 1.82,-1.515 c 16.304,-14.849 29.468,-26.92 38.776,-35.467 3.031,-2.785 5.607,-5.155 7.78,-7.141 5.725,5.002 11.64,10.158 17.811,15.51 l 4.874,4.244 c 8.223,7.155 16.723,14.554 26.22,22.943 l 1.487,1.335 20.792,0.851 c 28.382,28.494 56.842,56.91 85.48,85.144 40.61,40.041 64.901,87.796 70.125,144.617 5.256,57.167 -9.279,109.234 -42.801,155.944"
              fill={color}
              transform="matrix(1.3333333,0,0,-1.3333333,805.31253,825.86973)"
              clipPath="url(#clipPath6)"
            />
          </G>
        </G>
      </Svg>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  svg: {
    flex: 1,
  },
});
