import { useEffect, useState } from 'react'
import { doc, onSnapshot } from 'firebase/firestore'
import { db } from '@/lib/firebase'

export function useDriverLiveLocation(driverId?: string | null) {
  const [location, setLocation] = useState<{ lat: number; lng: number; bearing?: number } | null>(null)

  useEffect(() => {
    if (!driverId) return

    const ref = doc(db, 'drivers', driverId)
    const unsub = onSnapshot(ref, (snap) => {
      const data = snap.data() as any
      const coords = data?.location?.current
      const heading = data?.location?.heading
      if (coords?.latitude != null && coords?.longitude != null) {
        setLocation({
          lat: coords.latitude,
          lng: coords.longitude,
          bearing: typeof heading === 'number' ? heading : 0
        })
      }
    })

    return () => unsub()
  }, [driverId])

  return location
}

export default useDriverLiveLocation
