import { useState, useEffect, useCallback, useRef } from 'react'
import { RideRequestDocument, RideRequestStatus, RideRequestFilters, RideRequestStats } from '@/types/database'
import { RideRequestService, CreateRideRequestData } from '@/services/RideRequestService'
import { QueryDocumentSnapshot, DocumentData } from 'firebase/firestore'

export interface UseRideRequestsReturn {
  // State
  requests: RideRequestDocument[]
  loading: boolean
  error: string | null
  stats: RideRequestStats | null
  hasMore: boolean
  lastDoc: QueryDocumentSnapshot<DocumentData> | null

  // Actions
  createRideRequest: (data: CreateRideRequestData) => Promise<string>
  acceptRideRequest: (requestId: string, driverId: string, driverData: any) => Promise<string>
  rejectRideRequest: (requestId: string, driverId: string, reason?: string) => Promise<void>
  cancelRideRequest: (requestId: string, reason?: string) => Promise<void>
  updateRideRequestStatus: (requestId: string, status: RideRequestStatus, notes?: string, driverId?: string) => Promise<void>
  loadRideRequests: (filters?: RideRequestFilters, reset?: boolean) => Promise<void>
  loadMore: () => Promise<void>
  refreshRequests: () => Promise<void>
  loadStats: () => Promise<void>
  subscribeToCustomerRequests: (customerId: string) => void
  subscribeToPendingRequests: () => void
  unsubscribeAll: () => void
  deleteRideRequest: (requestId: string) => Promise<void>
}

export function useRideRequests(): UseRideRequestsReturn {
  // State
  const [requests, setRequests] = useState<RideRequestDocument[]>([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [stats, setStats] = useState<RideRequestStats | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [lastDoc, setLastDoc] = useState<QueryDocumentSnapshot<DocumentData> | null>(null)

  // Refs for stable values
  const loadingRef = useRef(false)
  const hasMoreRef = useRef(true)
  const filtersRef = useRef<RideRequestFilters>({})
  const unsubscribeRefs = useRef<(() => void)[]>([])

  // Update refs when state changes
  useEffect(() => {
    loadingRef.current = loading
  }, [loading])

  useEffect(() => {
    hasMoreRef.current = hasMore
  }, [hasMore])

  // Create ride request
  const createRideRequest = useCallback(async (data: CreateRideRequestData): Promise<string> => {
    try {
      setError(null)
      const requestId = await RideRequestService.createRideRequest(data)
      
      // Refresh requests to show the new one
      await loadRideRequests(filtersRef.current, true)
      
      return requestId
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to create ride request'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Accept ride request
  const acceptRideRequest = useCallback(async (requestId: string, driverId: string, driverData: any): Promise<string> => {
    try {
      setError(null)
      const orderId = await RideRequestService.acceptRideRequest(requestId, driverId, driverData)
      
      // Refresh requests to update the accepted one
      await loadRideRequests(filtersRef.current, true)
      
      return orderId
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to accept ride request'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Reject ride request
  const rejectRideRequest = useCallback(async (requestId: string, driverId: string, reason?: string): Promise<void> => {
    try {
      setError(null)
      await RideRequestService.rejectRideRequest(requestId, driverId, reason)
      
      // Refresh requests to update the rejected one
      await loadRideRequests(filtersRef.current, true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to reject ride request'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Cancel ride request
  const cancelRideRequest = useCallback(async (requestId: string, reason?: string): Promise<void> => {
    try {
      setError(null)
      await RideRequestService.cancelRideRequest(requestId, reason)
      
      // Refresh requests to update the cancelled one
      await loadRideRequests(filtersRef.current, true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to cancel ride request'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Update ride request status
  const updateRideRequestStatus = useCallback(async (requestId: string, status: RideRequestStatus, notes?: string, driverId?: string): Promise<void> => {
    try {
      setError(null)
      await RideRequestService.updateRideRequestStatus(requestId, status, notes, driverId)
      
      // Refresh requests to update the status
      await loadRideRequests(filtersRef.current, true)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to update ride request status'
      setError(errorMessage)
      throw new Error(errorMessage)
    }
  }, [])

  // Load ride requests
  const loadRideRequests = useCallback(async (filters: RideRequestFilters = {}, reset: boolean = false) => {
    if (loadingRef.current) return

    try {
      setLoading(true)
      setError(null)

      const currentLastDoc = reset ? null : lastDoc
      const result = await RideRequestService.getRideRequests(filters, currentLastDoc)

      if (reset) {
        setRequests(result.requests)
      } else {
        setRequests(prev => [...prev, ...result.requests])
      }

      setLastDoc(result.lastDoc || null)
      setHasMore(result.hasMore)
      filtersRef.current = filters
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load ride requests'
      setError(errorMessage)
    } finally {
      setLoading(false)
    }
  }, [lastDoc])

  // Load more requests
  const loadMore = useCallback(async () => {
    if (!hasMoreRef.current || loadingRef.current) return
    await loadRideRequests(filtersRef.current, false)
  }, [loadRideRequests])

  // Refresh requests
  const refreshRequests = useCallback(async () => {
    await loadRideRequests(filtersRef.current, true)
  }, [loadRideRequests])

  // Load stats
  const loadStats = useCallback(async () => {
    try {
      setError(null)
      const statsData = await RideRequestService.getRideRequestStats()
      setStats(statsData)
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to load stats'
      setError(errorMessage)
    }
  }, [])

  // Subscribe to customer requests
  const subscribeToCustomerRequests = useCallback((customerId: string) => {
    const unsubscribe = RideRequestService.subscribeToCustomerRequests(customerId, (newRequests) => {
      setRequests(newRequests)
    })
    unsubscribeRefs.current.push(unsubscribe)
  }, [])

  // Subscribe to pending requests
  const subscribeToPendingRequests = useCallback(() => {
    const unsubscribe = RideRequestService.subscribeToPendingRequests((newRequests) => {
      setRequests(newRequests)
    })
    unsubscribeRefs.current.push(unsubscribe)
  }, [])

  // Unsubscribe from all listeners
  const unsubscribeAll = useCallback(() => {
    unsubscribeRefs.current.forEach(unsubscribe => unsubscribe())
    unsubscribeRefs.current = []
  }, [])

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      unsubscribeAll()
    }
  }, [unsubscribeAll])

  // Delete ride request
  const deleteRideRequest = useCallback(async (requestId: string): Promise<void> => {
    try {
      setError(null);
      await RideRequestService.deleteRideRequest(requestId);
      
      // Update local state by removing the deleted request
      setRequests(prevRequests => prevRequests.filter(request => request.id !== requestId));
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete ride request';
      setError(errorMessage);
      throw err;
    }
  }, []);

  return {
    // State
    requests,
    loading,
    error,
    stats,
    hasMore,
    lastDoc,

    // Actions
    createRideRequest,
    acceptRideRequest,
    rejectRideRequest,
    cancelRideRequest,
    updateRideRequestStatus,
    loadRideRequests,
    loadMore,
    refreshRequests,
    loadStats,
    subscribeToCustomerRequests,
    subscribeToPendingRequests,
    unsubscribeAll,
    deleteRideRequest
  }
}
