import { useState, useEffect } from 'react'
import {
  collection,
  addDoc,
  query,
  where,
  orderBy,
  limit,
  onSnapshot,
  serverTimestamp,
  Timestamp,
  Unsubscribe
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { NotificationDocument } from '@/types/database'
import toast from 'react-hot-toast'

export interface CreateNotificationData {
  userId: string
  type: 'order_update' | 'payment' | 'promotion' | 'system'
  title: string
  body: string
  data?: Record<string, any>
  scheduledFor?: Date
}

export class NotificationService {
  private static readonly COLLECTION = 'notifications'

  /**
   * Create a new notification
   */
  static async createNotification(notificationData: CreateNotificationData): Promise<string> {
    try {
      // Clean data object to remove undefined values
      const cleanData: Record<string, any> = {}
      if (notificationData.data) {
        Object.entries(notificationData.data).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            cleanData[key] = value
          }
        })
      }

      const notification: any = {
        userId: notificationData.userId,
        type: notificationData.type,
        title: notificationData.title,
        body: notificationData.body,
        data: cleanData,
        read: false,
        createdAt: serverTimestamp() as Timestamp
      }

      // Only add scheduledFor if it's provided (avoid undefined values)
      if (notificationData.scheduledFor) {
        notification.scheduledFor = Timestamp.fromDate(notificationData.scheduledFor)
      }

      const docRef = await addDoc(collection(db, this.COLLECTION), notification)
      return docRef.id
    } catch (error) {
      console.error('Error creating notification:', error)
      throw new Error('Failed to create notification')
    }
  }

  /**
   * Send ride-related notifications
   */
  static async sendRideNotification(
    userId: string,
    orderId: string,
    type: 'ride_request' | 'ride_accepted' | 'driver_arriving' | 'driver_arrived' | 'ride_started' | 'ride_completed',
    additionalData?: Record<string, any>
  ): Promise<void> {
    try {
      const notifications = {
        ride_request: {
          title: '🚗 New Ride Request',
          body: 'A customer is requesting a ride in your area'
        },
        ride_accepted: {
          title: '✅ Ride Accepted',
          body: 'Your driver is on the way to pick you up'
        },
        driver_arriving: {
          title: '🚕 Driver Arriving',
          body: 'Your driver is arriving at the pickup location'
        },
        driver_arrived: {
          title: '📍 Driver Arrived',
          body: 'Your driver has arrived at the pickup location'
        },
        ride_started: {
          title: '🛣️ Ride Started',
          body: 'Your ride has started. Enjoy your trip!'
        },
        ride_completed: {
          title: '🎉 Ride Completed',
          body: 'Your ride has been completed. Thank you for using Yellow Taxi!'
        }
      }

      const notificationConfig = notifications[type]
      if (!notificationConfig) {
        throw new Error(`Unknown notification type: ${type}`)
      }

      await this.createNotification({
        userId,
        type: 'order_update',
        title: notificationConfig.title,
        body: notificationConfig.body,
        data: {
          orderId,
          notificationType: type,
          ...additionalData
        }
      })

      // Also show browser toast notification
      this.showBrowserNotification(notificationConfig.title, notificationConfig.body)
    } catch (error) {
      console.error('Error sending ride notification:', error)
    }
  }

  /**
   * Subscribe to user notifications
   */
  static subscribeToUserNotifications(
    userId: string,
    callback: (notifications: NotificationDocument[]) => void
  ): Unsubscribe {
    const q = query(
      collection(db, this.COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc'),
      limit(50)
    )

    return onSnapshot(q, (snapshot) => {
      const notifications = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as NotificationDocument[]
      
      callback(notifications)
    }, (error) => {
      console.error('Error listening to notifications:', error)
      callback([])
    })
  }

  /**
   * Show browser notification (if permission granted)
   */
  static showBrowserNotification(title: string, body: string, icon?: string): void {
    // Check if browser supports notifications
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications')
      return
    }

    // Check permission
    if (Notification.permission === 'granted') {
      new Notification(title, {
        body,
        icon: icon || '/logo.svg',
        badge: '/logo.svg',
        tag: 'yellow-taxi-notification'
      })
    } else if (Notification.permission !== 'denied') {
      // Request permission
      Notification.requestPermission().then(permission => {
        if (permission === 'granted') {
          new Notification(title, {
            body,
            icon: icon || '/logo.svg',
            badge: '/logo.svg',
            tag: 'yellow-taxi-notification'
          })
        }
      })
    }

    // Also show toast notification
    toast.success(`${title}\n${body}`, {
      duration: 5000,
      position: 'top-right'
    })
  }

  /**
   * Request notification permission
   */
  static async requestNotificationPermission(): Promise<NotificationPermission> {
    if (!('Notification' in window)) {
      console.log('This browser does not support notifications')
      return 'denied'
    }

    if (Notification.permission === 'default') {
      const permission = await Notification.requestPermission()
      return permission
    }

    return Notification.permission
  }

  /**
   * Send notification to multiple users
   */
  static async sendBulkNotifications(
    userIds: string[],
    notificationData: Omit<CreateNotificationData, 'userId'>
  ): Promise<void> {
    try {
      const promises = userIds.map(userId =>
        this.createNotification({
          ...notificationData,
          userId
        })
      )

      await Promise.all(promises)
    } catch (error) {
      console.error('Error sending bulk notifications:', error)
      throw new Error('Failed to send bulk notifications')
    }
  }

  /**
   * Send notification to all drivers in an area (for ride requests)
   */
  static async notifyNearbyDrivers(
    pickupLocation: { lat: number; lng: number },
    orderId: string,
    customerName: string,
    fare: number
  ): Promise<void> {
    try {
      // In a real app, you'd query for drivers within a certain radius
      // For demo purposes, we'll simulate notifying some drivers
      const mockDriverIds = ['driver1', 'driver2', 'driver3'] // These would be actual driver IDs

      await this.sendBulkNotifications(mockDriverIds, {
        type: 'order_update',
        title: '🚗 New Ride Request',
        body: `${customerName} needs a ride - JD ${fare.toFixed(2)}`,
        data: {
          orderId,
          customerName,
          fare,
          pickupLocation,
          notificationType: 'ride_request'
        }
      })

      console.log(`Notified ${mockDriverIds.length} nearby drivers about order ${orderId}`)
    } catch (error) {
      console.error('Error notifying nearby drivers:', error)
    }
  }
}

// Hook for managing notifications
export function useNotifications(userId?: string) {
  const [notifications, setNotifications] = useState<NotificationDocument[]>([])
  const [unreadCount, setUnreadCount] = useState(0)

  useEffect(() => {
    if (!userId) return

    const unsubscribe = NotificationService.subscribeToUserNotifications(
      userId,
      (newNotifications) => {
        setNotifications(newNotifications)
        setUnreadCount(newNotifications.filter(n => !n.read).length)
      }
    )

    return unsubscribe
  }, [userId])

  // Request notification permission on first use
  useEffect(() => {
    NotificationService.requestNotificationPermission()
  }, [])

  return {
    notifications,
    unreadCount
  }
}

export default NotificationService
