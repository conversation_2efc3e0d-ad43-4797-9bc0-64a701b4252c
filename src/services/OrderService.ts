import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter, 
  onSnapshot,
  serverTimestamp,
  Timestamp,
  GeoPoint,
  QueryDocumentSnapshot,
  DocumentData,
  Unsubscribe
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { OrderDocument, OrderStatus, OrderEvent, UserDocument } from '@/types/database'
import { NotificationService } from './NotificationService'

export interface CreateOrderData {
  customerId: string
  pickup: {
    address: string
    coordinates: { lat: number; lng: number }
    placeId?: string
  }
  destination: {
    address: string
    coordinates: { lat: number; lng: number }
    placeId?: string
  }
  serviceType: 'standard' | 'premium' | 'economy'
  paymentMethod: 'cash' | 'card' | 'wallet'
  notes?: string
}

export interface OrderFilters {
  status?: OrderStatus | 'all'
  customerId?: string
  driverId?: string
  dateFrom?: Date
  dateTo?: Date
  search?: string
}

export interface OrderStats {
  total: number
  pending: number
  active: number
  completed: number
  cancelled: number
  revenue: number
}

export class OrderService {
  private static readonly COLLECTION = 'orders'
  private static readonly BATCH_SIZE = 50

  /**
   * Create a new ride order
   */
  static async createOrder(orderData: CreateOrderData): Promise<string> {
    try {
      // Get customer information
      const customerDoc = await getDoc(doc(db, 'users', orderData.customerId))
      if (!customerDoc.exists()) {
        throw new Error('Customer not found')
      }

      const customer = customerDoc.data() as UserDocument
      const now = Timestamp.now()

      // Calculate estimated pricing (basic calculation)
      const distance = this.calculateDistance(
        orderData.pickup.coordinates,
        orderData.destination.coordinates
      )
      const pricing = this.calculatePricing(distance, orderData.serviceType)

      const order: Omit<OrderDocument, 'id'> = {
        customer: {
          id: orderData.customerId,
          name: `${customer.profile.firstName} ${customer.profile.lastName}`,
          phone: customer.profile.phone,
          avatar: customer.profile.avatar || ''
        },
        service: {
          categoryId: 'taxi',
          categoryName: 'Yellow Taxi',
          type: orderData.serviceType
        },
        locations: {
          pickup: {
            address: orderData.pickup.address,
            coordinates: new GeoPoint(
              orderData.pickup.coordinates.lat,
              orderData.pickup.coordinates.lng
            ),
            placeId: orderData.pickup.placeId || ''
          },
          destination: {
            address: orderData.destination.address,
            coordinates: new GeoPoint(
              orderData.destination.coordinates.lat,
              orderData.destination.coordinates.lng
            ),
            placeId: orderData.destination.placeId || ''
          },
          route: {
            distance,
            duration: Math.round(distance * 2), // Rough estimate: 2 minutes per km
            encodedPolyline: '' // Will be calculated by Google Maps
          }
        },
        pricing,
        status: {
          current: 'pending',
          timeline: [{
            status: 'pending',
            timestamp: now,
            notes: 'Order created'
          }]
        },
        payment: {
          method: orderData.paymentMethod,
          status: 'pending'
        },
        tracking: {
          realTimeEnabled: true,
          driverMovements: [],
          estimatedRoute: []
        },
        ratings: {},
        metadata: {
          createdAt: now,
          updatedAt: now,
          version: 1
        },
        notes: orderData.notes || ''
      }

      const docRef = await addDoc(collection(db, this.COLLECTION), order)

      // Update the document with its ID
      await updateDoc(docRef, { id: docRef.id })

      // Notify nearby drivers about the new ride request
      await NotificationService.notifyNearbyDrivers(
        orderData.pickup.coordinates,
        docRef.id,
        order.customer.name,
        order.pricing.total
      )

      return docRef.id
    } catch (error) {
      console.error('Error creating order:', error)
      throw new Error('Failed to create order')
    }
  }

  /**
   * Create a new ride order with driver already assigned
   */
  static async createOrderWithDriver(
    orderData: CreateOrderData,
    driverData: {
      id: string
      name: string
      phone: string
      avatar?: string
      vehicle: any
      location: { lat: number; lng: number }
    }
  ): Promise<string> {
    try {
      // Get customer information
      const customerDoc = await getDoc(doc(db, 'users', orderData.customerId))
      if (!customerDoc.exists()) {
        throw new Error('Customer not found')
      }

      const customer = customerDoc.data() as UserDocument
      const now = Timestamp.now()

      // Calculate estimated pricing (basic calculation)
      const distance = this.calculateDistance(
        orderData.pickup.coordinates,
        orderData.destination.coordinates
      )
      const pricing = this.calculatePricing(distance, orderData.serviceType)

      const order: Omit<OrderDocument, 'id'> = {
        customer: {
          id: orderData.customerId,
          name: `${customer.profile.firstName} ${customer.profile.lastName}`,
          phone: customer.profile.phone,
          avatar: customer.profile.avatar || ''
        },
        driver: {
          id: driverData.id,
          name: driverData.name,
          phone: driverData.phone,
          avatar: driverData.avatar || '',
          vehicle: driverData.vehicle,
          location: new GeoPoint(driverData.location.lat, driverData.location.lng)
        },
        service: {
          categoryId: 'taxi',
          categoryName: 'Yellow Taxi',
          type: orderData.serviceType
        },
        locations: {
          pickup: {
            address: orderData.pickup.address,
            coordinates: new GeoPoint(
              orderData.pickup.coordinates.lat,
              orderData.pickup.coordinates.lng
            ),
            placeId: orderData.pickup.placeId || ''
          },
          destination: {
            address: orderData.destination.address,
            coordinates: new GeoPoint(
              orderData.destination.coordinates.lat,
              orderData.destination.coordinates.lng
            ),
            placeId: orderData.destination.placeId || ''
          },
          route: {
            distance,
            duration: Math.round(distance * 2), // Rough estimate: 2 minutes per km
            encodedPolyline: '' // Will be calculated by Google Maps
          }
        },
        pricing,
        status: {
          current: 'assigned',
          timeline: [{
            status: 'assigned',
            timestamp: now,
            notes: 'Order created and driver assigned'
          }],
          estimatedArrival: Timestamp.fromDate(
            new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
          )
        },
        payment: {
          method: orderData.paymentMethod,
          status: 'pending'
        },
        tracking: {
          realTimeEnabled: true,
          driverMovements: [],
          estimatedRoute: []
        },
        ratings: {},
        metadata: {
          createdAt: now,
          updatedAt: now,
          version: 1
        },
        notes: orderData.notes || ''
      }

      const docRef = await addDoc(collection(db, this.COLLECTION), order)

      // Update the document with its ID
      await updateDoc(docRef, { id: docRef.id })

      // Send notifications
      await NotificationService.createNotification({
        userId: orderData.customerId,
        type: 'order_update',
        title: 'Ride Confirmed!',
        body: `Your ride has been confirmed. ${driverData.name} is on the way.`,
        data: { orderId: docRef.id, status: 'assigned' }
      })

      return docRef.id
    } catch (error) {
      console.error('Error creating order with driver:', error)
      throw new Error('Failed to create order with driver')
    }
  }

  /**
   * Get order by ID
   */
  static async getOrder(orderId: string): Promise<OrderDocument | null> {
    try {
      const docRef = doc(db, this.COLLECTION, orderId)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as OrderDocument
      }
      
      return null
    } catch (error) {
      console.error('Error getting order:', error)
      throw new Error('Failed to get order')
    }
  }

  /**
   * Update order status
   */
  static async updateOrderStatus(
    orderId: string, 
    status: OrderStatus, 
    notes?: string,
    driverData?: {
      id: string
      name: string
      phone: string
      avatar?: string
      vehicle: any
      location: { lat: number; lng: number }
    }
  ): Promise<void> {
    try {
      const orderRef = doc(db, this.COLLECTION, orderId)
      const now = Timestamp.now()

      const updateData: any = {
        'status.current': status,
        'status.timeline': [...(await this.getOrderTimeline(orderId)), {
          status,
          timestamp: now,
          notes: notes || `Status updated to ${status}`
        }],
        'metadata.updatedAt': now
      }

      // Add driver information when order is assigned
      if (status === 'assigned' && driverData) {
        updateData.driver = {
          id: driverData.id,
          name: driverData.name,
          phone: driverData.phone,
          avatar: driverData.avatar || '',
          vehicle: driverData.vehicle,
          location: new GeoPoint(driverData.location.lat, driverData.location.lng)
        }
      }

      // Set estimated times
      if (status === 'assigned') {
        updateData['status.estimatedArrival'] = Timestamp.fromDate(
          new Date(Date.now() + 10 * 60 * 1000) // 10 minutes from now
        )
      }

      if (status === 'picked_up') {
        updateData['status.estimatedCompletion'] = Timestamp.fromDate(
          new Date(Date.now() + 20 * 60 * 1000) // 20 minutes from now
        )
      }

      await updateDoc(orderRef, updateData)

      // Send notifications based on status change
      const order = await this.getOrder(orderId)
      if (order) {
        await this.sendStatusNotifications(order, status)
      }
    } catch (error) {
      console.error('Error updating order status:', error)
      throw new Error('Failed to update order status')
    }
  }

  /**
   * Get orders with filtering and pagination
   */
  static async getOrders(
    filters: OrderFilters = {},
    lastDoc?: QueryDocumentSnapshot<DocumentData>,
    pageSize: number = this.BATCH_SIZE
  ): Promise<{
    orders: OrderDocument[]
    lastDoc?: QueryDocumentSnapshot<DocumentData>
    hasMore: boolean
  }> {
    try {
      let q = collection(db, this.COLLECTION)
      const constraints = []

      // Apply filters
      if (filters.status && filters.status !== 'all') {
        constraints.push(where('status.current', '==', filters.status))
      }

      if (filters.customerId) {
        constraints.push(where('customer.id', '==', filters.customerId))
      }

      if (filters.driverId) {
        constraints.push(where('driver.id', '==', filters.driverId))
      }

      // Add ordering and pagination
      constraints.push(orderBy('metadata.createdAt', 'desc'))
      constraints.push(limit(pageSize))

      if (lastDoc) {
        constraints.push(startAfter(lastDoc))
      }

      const querySnapshot = await getDocs(query(q, ...constraints))
      const orders = querySnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as OrderDocument[]

      return {
        orders,
        lastDoc: querySnapshot.docs[querySnapshot.docs.length - 1],
        hasMore: querySnapshot.docs.length === pageSize
      }
    } catch (error) {
      console.error('Error getting orders:', error)
      throw new Error('Failed to get orders')
    }
  }

  /**
   * Listen to real-time order updates
   */
  static subscribeToOrder(orderId: string, callback: (order: OrderDocument | null) => void): Unsubscribe {
    const orderRef = doc(db, this.COLLECTION, orderId)
    
    return onSnapshot(orderRef, (doc) => {
      if (doc.exists()) {
        callback({ id: doc.id, ...doc.data() } as OrderDocument)
      } else {
        callback(null)
      }
    }, (error) => {
      console.error('Error listening to order updates:', error)
      callback(null)
    })
  }

  /**
   * Listen to orders for a specific user (customer or driver)
   */
  static subscribeToUserOrders(
    userId: string, 
    userType: 'customer' | 'driver',
    callback: (orders: OrderDocument[]) => void
  ): Unsubscribe {
    const field = userType === 'customer' ? 'customer.id' : 'driver.id'
    const q = query(
      collection(db, this.COLLECTION),
      where(field, '==', userId),
      orderBy('metadata.createdAt', 'desc'),
      limit(20)
    )
    
    return onSnapshot(q, (snapshot) => {
      const orders = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as OrderDocument[]
      callback(orders)
    }, (error) => {
      console.error('Error listening to user orders:', error)
      callback([])
    })
  }

  // Helper methods
  private static async getOrderTimeline(orderId: string): Promise<OrderEvent[]> {
    try {
      const order = await this.getOrder(orderId)
      return order?.status.timeline || []
    } catch {
      return []
    }
  }

  private static calculateDistance(
    point1: { lat: number; lng: number },
    point2: { lat: number; lng: number }
  ): number {
    // Haversine formula for calculating distance between two points
    const R = 6371 // Earth's radius in kilometers
    const dLat = this.toRadians(point2.lat - point1.lat)
    const dLng = this.toRadians(point2.lng - point1.lng)
    
    const a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
              Math.cos(this.toRadians(point1.lat)) * Math.cos(this.toRadians(point2.lat)) *
              Math.sin(dLng / 2) * Math.sin(dLng / 2)
    
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))
    return R * c
  }

  private static toRadians(degrees: number): number {
    return degrees * (Math.PI / 180)
  }

  private static calculatePricing(distance: number, serviceType: string) {
    const baseFare = serviceType === 'premium' ? 3 : serviceType === 'economy' ? 1.5 : 2
    const perKmRate = serviceType === 'premium' ? 1.5 : serviceType === 'economy' ? 0.8 : 1

    const distanceFare = distance * perKmRate
    const total = baseFare + distanceFare

    return {
      baseFare,
      distanceFare,
      timeFare: 0,
      surcharge: 0,
      discount: 0,
      total: Math.round(total * 100) / 100, // Round to 2 decimal places
      currency: 'JOD' as const,
      breakdown: {
        baseFare,
        distanceFare,
        total
      }
    }
  }

  /**
   * Send notifications based on order status changes
   */
  private static async sendStatusNotifications(order: OrderDocument, status: OrderStatus): Promise<void> {
    try {
      const notificationMap: Record<OrderStatus, string> = {
        'pending': 'ride_request',
        'searching': 'ride_request',
        'assigned': 'ride_accepted',
        'driver_arriving': 'driver_arriving',
        'driver_arrived': 'driver_arrived',
        'picked_up': 'ride_started',
        'in_progress': 'ride_started',
        'completed': 'ride_completed',
        'cancelled': 'ride_completed'
      }

      const notificationType = notificationMap[status]
      if (!notificationType) return

      // Notify customer
      const customerNotificationData: Record<string, any> = {
        fare: order.pricing.total
      }

      // Only add driverName if driver is assigned (avoid undefined values)
      if (order.driver?.name) {
        customerNotificationData.driverName = order.driver.name
      }

      await NotificationService.sendRideNotification(
        order.customer.id,
        order.id,
        notificationType as any,
        customerNotificationData
      )

      // Notify driver (if assigned)
      if (order.driver && ['assigned', 'driver_arriving', 'driver_arrived', 'picked_up', 'in_progress'].includes(status)) {
        await NotificationService.sendRideNotification(
          order.driver.id,
          order.id,
          notificationType as any,
          {
            customerName: order.customer.name,
            fare: order.pricing.total
          }
        )
      }
    } catch (error) {
      console.error('Error sending status notifications:', error)
    }
  }
}
