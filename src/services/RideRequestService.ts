import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  where, 
  orderBy, 
  limit, 
  startAfter, 
  onSnapshot,
  serverTimestamp,
  Timestamp,
  GeoPoint,
  QueryDocumentSnapshot,
  DocumentData,
  Unsubscribe
} from 'firebase/firestore'
import { db } from '@/lib/firebase'
import { RideRequestDocument, RideRequestStatus, RideRequestEvent, UserDocument } from '@/types/database'
import { NotificationService } from './NotificationService'

export interface CreateRideRequestData {
  customerId: string
  pickup: {
    address: string
    coordinates: { lat: number; lng: number }
    placeId?: string
  }
  destination: {
    address: string
    coordinates: { lat: number; lng: number }
    placeId?: string
  }
  serviceType: 'standard' | 'premium' | 'economy'
  paymentMethod: 'cash' | 'card' | 'wallet'
  notes?: string
}

export interface RideRequestFilters {
  status?: RideRequestStatus | 'all'
  customerId?: string
  driverId?: string
  dateFrom?: Date
  dateTo?: Date
  serviceType?: 'standard' | 'premium' | 'economy'
}

export interface RideRequestStats {
  total: number
  pending: number
  accepted: number
  rejected: number
  expired: number
  cancelled: number
}

export class RideRequestService {
  private static readonly COLLECTION = 'rideRequests'
  private static readonly BATCH_SIZE = 50
  private static readonly REQUEST_EXPIRY_MINUTES = 5 // Requests expire after 5 minutes

  /**
   * Create a new ride request
   */
  static async createRideRequest(requestData: CreateRideRequestData): Promise<string> {
    try {
      // Get customer information
      const customerDoc = await getDoc(doc(db, 'users', requestData.customerId))
      if (!customerDoc.exists()) {
        throw new Error('Customer not found')
      }

      const customer = customerDoc.data() as UserDocument
      const now = Timestamp.now()
      const expiresAt = Timestamp.fromDate(new Date(Date.now() + this.REQUEST_EXPIRY_MINUTES * 60 * 1000))

      // Calculate estimated pricing (basic calculation)
      const distance = this.calculateDistance(
        requestData.pickup.coordinates,
        requestData.destination.coordinates
      )
      const pricing = this.calculatePricing(distance, requestData.serviceType)

      const rideRequest: Omit<RideRequestDocument, 'id'> = {
        customer: {
          id: requestData.customerId,
          name: `${customer.profile.firstName} ${customer.profile.lastName}`,
          phone: customer.profile.phone,
          avatar: customer.profile.avatar || ''
        },
        service: {
          categoryId: 'taxi',
          categoryName: 'Yellow Taxi',
          type: requestData.serviceType
        },
        locations: {
          pickup: {
            address: requestData.pickup.address,
            coordinates: new GeoPoint(
              requestData.pickup.coordinates.lat,
              requestData.pickup.coordinates.lng
            ),
            placeId: requestData.pickup.placeId || ''
          },
          destination: {
            address: requestData.destination.address,
            coordinates: new GeoPoint(
              requestData.destination.coordinates.lat,
              requestData.destination.coordinates.lng
            ),
            placeId: requestData.destination.placeId || ''
          },
          route: {
            distance,
            duration: Math.round(distance * 2), // Rough estimate: 2 minutes per km
            encodedPolyline: '' // Will be calculated by Google Maps
          }
        },
        pricing,
        status: {
          current: 'pending',
          timeline: [{
            status: 'pending',
            timestamp: now,
            notes: 'Ride request created'
          }],
          expiresAt
        },
        payment: {
          method: requestData.paymentMethod
        },
        metadata: {
          createdAt: now,
          updatedAt: now,
          version: 1,
          source: 'web'
        },
        notes: requestData.notes || ''
      }

      const docRef = await addDoc(collection(db, this.COLLECTION), rideRequest)

      // Update the document with its ID
      await updateDoc(docRef, { id: docRef.id })

      // Notify nearby drivers about the new ride request
      await NotificationService.notifyNearbyDrivers(
        requestData.pickup.coordinates,
        docRef.id,
        rideRequest.customer.name,
        rideRequest.pricing.total
      )

      return docRef.id
    } catch (error) {
      console.error('Error creating ride request:', error)
      throw new Error('Failed to create ride request')
    }
  }

  /**
   * Get ride request by ID
   */
  static async getRideRequest(requestId: string): Promise<RideRequestDocument | null> {
    try {
      const docRef = doc(db, this.COLLECTION, requestId)
      const docSnap = await getDoc(docRef)
      
      if (docSnap.exists()) {
        return { id: docSnap.id, ...docSnap.data() } as RideRequestDocument
      }
      
      return null
    } catch (error) {
      console.error('Error getting ride request:', error)
      throw new Error('Failed to get ride request')
    }
  }

  /**
   * Update ride request status
   */
  static async updateRideRequestStatus(
    requestId: string, 
    status: RideRequestStatus, 
    notes?: string,
    driverId?: string
  ): Promise<void> {
    try {
      const requestRef = doc(db, this.COLLECTION, requestId)
      const now = Timestamp.now()

      const updateData: any = {
        'status.current': status,
        'status.timeline': [...(await this.getRideRequestTimeline(requestId)), {
          status,
          timestamp: now,
          notes: notes || `Status updated to ${status}`,
          driverId
        }],
        'metadata.updatedAt': now
      }

      // Set cancellation info if cancelled
      if (status === 'cancelled') {
        updateData['metadata.cancelledAt'] = now
        updateData['metadata.cancelledBy'] = driverId || 'system'
        updateData['metadata.cancellationReason'] = notes || 'Request cancelled'
      }

      await updateDoc(requestRef, updateData)

      // Send notifications based on status change
      const request = await this.getRideRequest(requestId)
      if (request) {
        await this.sendStatusNotifications(request, status, driverId)
      }
    } catch (error) {
      console.error('Error updating ride request status:', error)
      throw new Error('Failed to update ride request status')
    }
  }

  /**
   * Accept a ride request and create an order
   */
  static async acceptRideRequest(requestId: string, driverId: string, driverData: {
    name: string
    phone: string
    avatar?: string
    vehicle: any
    location: { lat: number; lng: number }
  }): Promise<string> {
    try {
      // Get the ride request
      const request = await this.getRideRequest(requestId)
      if (!request) {
        throw new Error('Ride request not found')
      }

      if (request.status.current !== 'pending') {
        throw new Error('Ride request is no longer available')
      }

      // Check if request has expired
      if (request.status.expiresAt.toDate() < new Date()) {
        await this.updateRideRequestStatus(requestId, 'expired', 'Request expired')
        throw new Error('Ride request has expired')
      }

      // Update request status to accepted
      await this.updateRideRequestStatus(requestId, 'accepted', 'Driver accepted the request', driverId)

      // Create order from the accepted request
      const { OrderService } = await import('./OrderService')
      const orderData = {
        customerId: request.customer.id,
        pickup: {
          address: request.locations.pickup.address,
          coordinates: {
            lat: request.locations.pickup.coordinates.latitude,
            lng: request.locations.pickup.coordinates.longitude
          },
          placeId: request.locations.pickup.placeId
        },
        destination: {
          address: request.locations.destination.address,
          coordinates: {
            lat: request.locations.destination.coordinates.latitude,
            lng: request.locations.destination.coordinates.longitude
          },
          placeId: request.locations.destination.placeId
        },
        serviceType: request.service.type,
        paymentMethod: request.payment.method,
        notes: request.notes
      }

      // Create order with driver already assigned
      const orderId = await OrderService.createOrderWithDriver(orderData, {
        id: driverId,
        name: driverData.name,
        phone: driverData.phone,
        avatar: driverData.avatar || '',
        vehicle: driverData.vehicle,
        location: driverData.location
      })

      return orderId
    } catch (error) {
      console.error('Error accepting ride request:', error)
      throw new Error('Failed to accept ride request')
    }
  }

  /**
   * Reject a ride request
   */
  static async rejectRideRequest(requestId: string, driverId: string, reason?: string): Promise<void> {
    try {
      await this.updateRideRequestStatus(requestId, 'rejected', reason || 'Driver rejected the request', driverId)
    } catch (error) {
      console.error('Error rejecting ride request:', error)
      throw new Error('Failed to reject ride request')
    }
  }

  /**
   * Cancel a ride request
   */
  static async cancelRideRequest(requestId: string, reason?: string): Promise<void> {
    try {
      await this.updateRideRequestStatus(requestId, 'cancelled', reason || 'Request cancelled by customer')
    } catch (error) {
      console.error('Error cancelling ride request:', error)
      throw new Error('Failed to cancel ride request')
    }
  }

  /**
   * Get ride requests with filtering and pagination
   */
  static async getRideRequests(
    filters: RideRequestFilters = {},
    lastDoc?: QueryDocumentSnapshot<DocumentData>,
    pageSize: number = this.BATCH_SIZE
  ): Promise<{
    requests: RideRequestDocument[]
    lastDoc?: QueryDocumentSnapshot<DocumentData>
    hasMore: boolean
  }> {
    try {
      let q = query(collection(db, this.COLLECTION), orderBy('metadata.createdAt', 'desc'))

      // Apply filters
      if (filters.status && filters.status !== 'all') {
        q = query(q, where('status.current', '==', filters.status))
      }

      if (filters.customerId) {
        q = query(q, where('customer.id', '==', filters.customerId))
      }

      if (filters.serviceType) {
        q = query(q, where('service.type', '==', filters.serviceType))
      }

      if (filters.dateFrom) {
        q = query(q, where('metadata.createdAt', '>=', Timestamp.fromDate(filters.dateFrom)))
      }

      if (filters.dateTo) {
        q = query(q, where('metadata.createdAt', '<=', Timestamp.fromDate(filters.dateTo)))
      }

      // Apply pagination
      if (lastDoc) {
        q = query(q, startAfter(lastDoc))
      }

      q = query(q, limit(pageSize + 1))

      const snapshot = await getDocs(q)
      const requests = snapshot.docs.slice(0, pageSize).map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as RideRequestDocument[]

      const hasMore = snapshot.docs.length > pageSize
      const newLastDoc = hasMore ? snapshot.docs[pageSize - 1] : undefined

      return {
        requests,
        lastDoc: newLastDoc,
        hasMore
      }
    } catch (error) {
      console.error('Error getting ride requests:', error)
      throw new Error('Failed to get ride requests')
    }
  }

  /**
   * Get ride request statistics
   */
  static async getRideRequestStats(): Promise<RideRequestStats> {
    try {
      const snapshot = await getDocs(collection(db, this.COLLECTION))
      const requests = snapshot.docs.map(doc => doc.data()) as RideRequestDocument[]

      const stats: RideRequestStats = {
        total: requests.length,
        pending: requests.filter(r => r.status.current === 'pending').length,
        accepted: requests.filter(r => r.status.current === 'accepted').length,
        rejected: requests.filter(r => r.status.current === 'rejected').length,
        expired: requests.filter(r => r.status.current === 'expired').length,
        cancelled: requests.filter(r => r.status.current === 'cancelled').length
      }

      return stats
    } catch (error) {
      console.error('Error getting ride request stats:', error)
      throw new Error('Failed to get ride request stats')
    }
  }

  /**
   * Subscribe to ride requests for a customer
   */
  static subscribeToCustomerRequests(
    customerId: string,
    callback: (requests: RideRequestDocument[]) => void
  ): Unsubscribe {
    const q = query(
      collection(db, this.COLLECTION),
      where('customer.id', '==', customerId),
      orderBy('metadata.createdAt', 'desc')
    )

    return onSnapshot(q, (snapshot) => {
      const requests = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as RideRequestDocument[]
      callback(requests)
    })
  }

  /**
   * Subscribe to pending ride requests for drivers
   */
  static subscribeToPendingRequests(
    callback: (requests: RideRequestDocument[]) => void
  ): Unsubscribe {
    const q = query(
      collection(db, this.COLLECTION),
      where('status.current', '==', 'pending'),
      orderBy('metadata.createdAt', 'desc')
    )

    return onSnapshot(q, (snapshot) => {
      const requests = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as RideRequestDocument[]
      callback(requests)
    })
  }

  /**
   * Clean up expired requests
   */
  static async cleanupExpiredRequests(): Promise<void> {
    try {
      const now = Timestamp.now()
      const q = query(
        collection(db, this.COLLECTION),
        where('status.current', '==', 'pending'),
        where('status.expiresAt', '<', now)
      )

      const snapshot = await getDocs(q)
      const batch = snapshot.docs.map(doc => 
        updateDoc(doc.ref, {
          'status.current': 'expired',
          'status.timeline': [...doc.data().status.timeline, {
            status: 'expired',
            timestamp: now,
            notes: 'Request expired'
          }],
          'metadata.updatedAt': now
        })
      )

      await Promise.all(batch)
    } catch (error) {
      console.error('Error cleaning up expired requests:', error)
    }
  }

  // Helper methods
  private static async getRideRequestTimeline(requestId: string): Promise<RideRequestEvent[]> {
    try {
      const request = await this.getRideRequest(requestId)
      return request?.status.timeline || []
    } catch {
      return []
    }
  }

  private static async sendStatusNotifications(
    request: RideRequestDocument, 
    status: RideRequestStatus,
    driverId?: string
  ): Promise<void> {
    try {
      const notifications = []

      // Notify customer
      if (status === 'accepted') {
        notifications.push(
          NotificationService.createNotification({
            userId: request.customer.id,
            type: 'order_update',
            title: 'Ride Request Accepted!',
            body: 'A driver has accepted your ride request and is on the way.',
            data: { requestId: request.id, status }
          })
        )
      } else if (status === 'rejected') {
        notifications.push(
          NotificationService.createNotification({
            userId: request.customer.id,
            type: 'order_update',
            title: 'Ride Request Rejected',
            body: 'Your ride request was rejected. Please try again.',
            data: { requestId: request.id, status }
          })
        )
      } else if (status === 'expired') {
        notifications.push(
          NotificationService.createNotification({
            userId: request.customer.id,
            type: 'order_update',
            title: 'Ride Request Expired',
            body: 'Your ride request has expired. Please try again.',
            data: { requestId: request.id, status }
          })
        )
      }

      await Promise.all(notifications)
    } catch (error) {
      console.error('Error sending status notifications:', error)
    }
  }

  private static calculateDistance(
    point1: { lat: number; lng: number },
    point2: { lat: number; lng: number }
  ): number {
    const R = 6371 // Earth's radius in kilometers
    const dLat = (point2.lat - point1.lat) * Math.PI / 180
    const dLng = (point2.lng - point1.lng) * Math.PI / 180
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(point1.lat * Math.PI / 180) * Math.cos(point2.lat * Math.PI / 180) * 
      Math.sin(dLng/2) * Math.sin(dLng/2)
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
    return R * c
  }

  private static calculatePricing(distance: number, serviceType: string): any {
    const baseFare = serviceType === 'premium' ? 3 : serviceType === 'economy' ? 1.5 : 2
    const pricePerKm = serviceType === 'premium' ? 0.8 : serviceType === 'economy' ? 0.4 : 0.6
    const pricePerMinute = serviceType === 'premium' ? 0.3 : serviceType === 'economy' ? 0.15 : 0.2

    const distanceFare = distance * pricePerKm
    const timeFare = (distance * 2) * pricePerMinute // Rough estimate
    const surcharge = 0
    const discount = 0
    const total = baseFare + distanceFare + timeFare + surcharge - discount

    return {
      baseFare,
      distanceFare,
      timeFare,
      surcharge,
      discount,
      total: Math.round(total * 100) / 100,
      currency: 'JOD',
      breakdown: {
        base: baseFare,
        distance: distanceFare,
        time: timeFare,
        surcharge,
        discount,
        total
      }
    }
  }

  /**
   * Delete a ride request by ID
   */
  static async deleteRideRequest(requestId: string): Promise<void> {
    try {
      const requestRef = doc(db, this.COLLECTION, requestId);
      await deleteDoc(requestRef);
    } catch (error) {
      console.error('Error deleting ride request:', error);
      throw new Error('Failed to delete ride request');
    }
  }
}
