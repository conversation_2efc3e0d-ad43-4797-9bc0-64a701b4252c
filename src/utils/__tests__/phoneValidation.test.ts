// Tests for phone validation utility
import {
  validatePhoneNumber,
  isValidPhoneForCountry,
  formatPhoneNumber,
  formatDisplayPhoneNumber,
  extractDialCode,
  getCountryByDialCode
} from '../phoneValidation';

describe('Phone Validation', () => {
  describe('validatePhoneNumber', () => {
    it('should validate US phone numbers correctly', () => {
      // Valid US numbers
      expect(validatePhoneNumber('5551234567', '+1')).toBe(true);
      expect(validatePhoneNumber('2125551234', '+1')).toBe(true);
      expect(validatePhoneNumber('3333333333', '+1')).toBe(true);
      
      // Invalid US numbers
      expect(validatePhoneNumber('1234567890', '+1')).toBe(false); // starts with 1
      expect(validatePhoneNumber('0123456789', '+1')).toBe(false); // starts with 0
      expect(validatePhoneNumber('555123456', '+1')).toBe(false);  // too short
      expect(validatePhoneNumber('55512345678', '+1')).toBe(false); // too long
    });

    it('should validate Jordan phone numbers correctly', () => {
      // Valid Jordan numbers
      expect(validatePhoneNumber('799123456', '+962')).toBe(true);
      expect(validatePhoneNumber('777888999', '+962')).toBe(true);
      
      // Invalid Jordan numbers
      expect(validatePhoneNumber('599123456', '+962')).toBe(false); // doesn't start with 7
      expect(validatePhoneNumber('79912345', '+962')).toBe(false);  // too short
      expect(validatePhoneNumber('7991234567', '+962')).toBe(false); // too long
    });

    it('should validate international format phone numbers', () => {
      // Valid international format
      expect(validatePhoneNumber('+15551234567')).toBe(true);
      expect(validatePhoneNumber('+962799123456')).toBe(true);
      expect(validatePhoneNumber('+13333333333')).toBe(true);
      
      // Invalid international format
      expect(validatePhoneNumber('+11234567890')).toBe(false); // US number starting with 1
      expect(validatePhoneNumber('+962599123456')).toBe(false); // Jordan number not starting with 7
    });
  });

  describe('isValidPhoneForCountry', () => {
    it('should validate US numbers correctly', () => {
      expect(isValidPhoneForCountry('5551234567', '+1')).toBe(true);
      expect(isValidPhoneForCountry('3333333333', '+1')).toBe(true);
      expect(isValidPhoneForCountry('1234567890', '+1')).toBe(false);
    });

    it('should validate Jordan numbers correctly', () => {
      expect(isValidPhoneForCountry('799123456', '+962')).toBe(true);
      expect(isValidPhoneForCountry('599123456', '+962')).toBe(false);
    });
  });

  describe('formatPhoneNumber', () => {
    it('should format phone numbers to E.164 format', () => {
      expect(formatPhoneNumber('5551234567', '+1')).toBe('+15551234567');
      expect(formatPhoneNumber('799123456', '+962')).toBe('+962799123456');
      expect(formatPhoneNumber('3333333333', '+1')).toBe('+13333333333');
    });

    it('should handle already formatted numbers', () => {
      expect(formatPhoneNumber('+15551234567', '+1')).toBe('+15551234567');
      expect(formatPhoneNumber('+962799123456', '+962')).toBe('+962799123456');
    });
  });

  describe('formatDisplayPhoneNumber', () => {
    it('should format US numbers for display', () => {
      expect(formatDisplayPhoneNumber('5551234567', '+1')).toBe('(*************');
      expect(formatDisplayPhoneNumber('3333333333', '+1')).toBe('(*************');
    });

    it('should format Jordan numbers for display', () => {
      expect(formatDisplayPhoneNumber('799123456', '+962')).toBe('799 123 456');
    });
  });

  describe('extractDialCode', () => {
    it('should extract dial codes from international numbers', () => {
      expect(extractDialCode('+15551234567')).toBe('+1');
      expect(extractDialCode('+962799123456')).toBe('+962');
      expect(extractDialCode('+13333333333')).toBe('+1');
    });

    it('should return null for numbers without dial codes', () => {
      expect(extractDialCode('5551234567')).toBe(null);
      expect(extractDialCode('799123456')).toBe(null);
    });
  });

  describe('getCountryByDialCode', () => {
    it('should return country info for valid dial codes', () => {
      const usCountry = getCountryByDialCode('+1');
      expect(usCountry?.name).toBe('United States');
      expect(usCountry?.code).toBe('US');

      const jordanCountry = getCountryByDialCode('+962');
      expect(jordanCountry?.name).toBe('Jordan');
      expect(jordanCountry?.code).toBe('JO');
    });

    it('should return undefined for invalid dial codes', () => {
      expect(getCountryByDialCode('+999')).toBeUndefined();
    });
  });
});
