// Firebase Test Utility
// This file helps test Firebase configuration and authentication

import { isFirebaseConfigured, firebaseAuth } from '../config/firebase';

export const testFirebaseConfiguration = () => {
  console.log('🔥 Testing Firebase Configuration...');
  
  const isConfigured = isFirebaseConfigured();
  console.log('Firebase configured:', isConfigured);
  
  if (isConfigured) {
    console.log('✅ Firebase is properly configured');
    console.log('Auth service available:', !!firebaseAuth);
    console.log('Current user:', firebaseAuth.currentUser?.uid || 'No user signed in');
  } else {
    console.log('❌ Firebase is not configured');
    console.log('Make sure you have:');
    console.log('1. google-services.json in android/app/');
    console.log('2. GoogleService-Info.plist in ios/yellowtaxiapp/');
    console.log('3. Firebase project properly set up');
  }
  
  return isConfigured;
};

export const testPhoneAuthFlow = async (phoneNumber: string) => {
  console.log('🔥 Testing Phone Authentication Flow...');
  
  if (!isFirebaseConfigured()) {
    console.log('❌ Firebase not configured, cannot test phone auth');
    return false;
  }
  
  try {
    console.log(`Attempting to send OTP to: ${phoneNumber}`);
    
    // This would normally send an actual OTP
    // For testing, we just check if the method is available
    const confirmationResult = await firebaseAuth.signInWithPhoneNumber(phoneNumber);
    
    console.log('✅ Phone auth method available');
    console.log('Confirmation result:', !!confirmationResult);
    
    return true;
  } catch (error) {
    console.log('❌ Phone auth test failed:', error);
    return false;
  }
};

// Helper to format phone numbers for testing
export const formatTestPhoneNumber = (phone: string, countryCode: string = '+962'): string => {
  const cleaned = phone.replace(/\D/g, '');
  
  if (countryCode === '+962') {
    // Jordan format: +962XXXXXXXXX
    if (cleaned.startsWith('962')) {
      return `+${cleaned}`;
    } else {
      return `+962${cleaned}`;
    }
  }
  
  return `${countryCode}${cleaned}`;
};

// Test phone numbers for development (these won't actually send SMS)
export const TEST_PHONE_NUMBERS = {
  jordan: '+962790000000',
  us: '+15551234567',
  uk: '+447700900000',
};

export default {
  testFirebaseConfiguration,
  testPhoneAuthFlow,
  formatTestPhoneNumber,
  TEST_PHONE_NUMBERS,
};
