/**
 * Utility functions for working with Google Maps polylines
 */

// Type definitions for Google Maps
interface DirectionsResult {
  routes: Array<{
    overview_path: Array<{
      lat(): number
      lng(): number
    }>
  }>
}

interface DirectionsStatus {
  OK: string
}

interface TravelMode {
  DRIVING: string
}

declare global {
  interface Window {
    google: {
      maps: {
        DirectionsService: new () => {
          route: (request: unknown, callback: (result: DirectionsResult | null, status: string) => void) => void
        }
        DirectionsStatus: DirectionsStatus
        TravelMode: TravelMode
      }
    }
  }
}

/**
 * Decode a Google Maps encoded polyline string into an array of lat/lng coordinates
 * @param encodedPolyline - The encoded polyline string from Google Maps
 * @returns Array of {lat, lng} coordinates
 */
export function decodePolyline(encodedPolyline: string): { lat: number; lng: number }[] {
  if (!encodedPolyline) return []

  const points: { lat: number; lng: number }[] = []
  let index = 0
  let lat = 0
  let lng = 0

  while (index < encodedPolyline.length) {
    let b: number
    let shift = 0
    let result = 0

    do {
      b = encodedPolyline.charCodeAt(index++) - 63
      result |= (b & 0x1f) << shift
      shift += 5
    } while (b >= 0x20)

    const dlat = (result & 1) !== 0 ? ~(result >> 1) : (result >> 1)
    lat += dlat

    shift = 0
    result = 0

    do {
      b = encodedPolyline.charCodeAt(index++) - 63
      result |= (b & 0x1f) << shift
      shift += 5
    } while (b >= 0x20)

    const dlng = (result & 1) !== 0 ? ~(result >> 1) : (result >> 1)
    lng += dlng

    points.push({
      lat: lat / 1e5,
      lng: lng / 1e5
    })
  }

  return points
}

/**
 * Get route coordinates using Google Maps DirectionsService
 * @param origin - Starting point
 * @param destination - End point
 * @returns Promise that resolves to array of route coordinates
 */
export function getRouteCoordinates(
  origin: { lat: number; lng: number },
  destination: { lat: number; lng: number }
): Promise<{ lat: number; lng: number }[]> {
  return new Promise((resolve, reject) => {
    if (!window.google || !window.google.maps) {
      reject(new Error('Google Maps not loaded'))
      return
    }

    const directionsService = new window.google.maps.DirectionsService()
    
    directionsService.route({
      origin: origin,
      destination: destination,
      travelMode: window.google.maps.TravelMode.DRIVING
    }, (result: DirectionsResult | null, status: string) => {
      if (status === window.google.maps.DirectionsStatus.OK && result) {
        const route = result.routes[0]
        const path = route.overview_path || []
        resolve(path.map((point) => ({
          lat: point.lat(),
          lng: point.lng()
        })))
      } else {
        reject(new Error(`Directions request failed: ${status}`))
      }
    })
  })
}

/**
 * Find the closest point on a route to a given location
 * @param location - The location to find closest point for
 * @param route - Array of route coordinates
 * @returns The closest point on the route and its index
 */
export function findClosestPointOnRoute(
  location: { lat: number; lng: number },
  route: { lat: number; lng: number }[]
): { point: { lat: number; lng: number }; index: number; distance: number } {
  let closestIndex = 0
  let minDistance = Number.MAX_VALUE

  for (let i = 0; i < route.length; i++) {
    const distance = calculateDistance(location, route[i])
    if (distance < minDistance) {
      minDistance = distance
      closestIndex = i
    }
  }

  return {
    point: route[closestIndex],
    index: closestIndex,
    distance: minDistance
  }
}

/**
 * Calculate distance between two points using Haversine formula
 * @param point1 - First point
 * @param point2 - Second point
 * @returns Distance in meters
 */
export function calculateDistance(
  point1: { lat: number; lng: number },
  point2: { lat: number; lng: number }
): number {
  const R = 6371000 // Earth's radius in meters
  const φ1 = point1.lat * Math.PI / 180
  const φ2 = point2.lat * Math.PI / 180
  const Δφ = (point2.lat - point1.lat) * Math.PI / 180
  const Δλ = (point2.lng - point1.lng) * Math.PI / 180

  const a = Math.sin(Δφ / 2) * Math.sin(Δφ / 2) +
    Math.cos(φ1) * Math.cos(φ2) *
    Math.sin(Δλ / 2) * Math.sin(Δλ / 2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a))

  return R * c
}

/**
 * Calculate bearing (direction) from one point to another
 * @param from - Starting point
 * @param to - Destination point
 * @returns Bearing in degrees (0-360, where 0 is North)
 */
export function calculateBearing(
  from: { lat: number; lng: number },
  to: { lat: number; lng: number }
): number {
  const φ1 = from.lat * Math.PI / 180
  const φ2 = to.lat * Math.PI / 180
  const Δλ = (to.lng - from.lng) * Math.PI / 180

  const y = Math.sin(Δλ) * Math.cos(φ2)
  const x = Math.cos(φ1) * Math.sin(φ2) - Math.sin(φ1) * Math.cos(φ2) * Math.cos(Δλ)

  let bearing = Math.atan2(y, x) * 180 / Math.PI
  bearing = (bearing + 360) % 360 // Normalize to 0-360 degrees

  return bearing
}

/**
 * Get the next point on the route based on current position and direction
 * @param currentLocation - Current location
 * @param route - Array of route coordinates
 * @param stepSize - Distance to move in meters
 * @returns Next location on the route
 */
export function getNextPointOnRoute(
  currentLocation: { lat: number; lng: number },
  route: { lat: number; lng: number }[],
  stepSize: number = 60
): { lat: number; lng: number } | null {
  if (route.length === 0) {
    console.log('Route is empty')
    return null
  }

  const { index: closestIndex, distance: closestDistance } = findClosestPointOnRoute(currentLocation, route)
  console.log(`Closest point on route: index ${closestIndex}, distance ${closestDistance.toFixed(2)}m`)
  
  // Start from the closest point and move forward
  let currentIndex = closestIndex
  let totalDistance = 0

  while (currentIndex < route.length - 1) {
    const currentPoint = route[currentIndex]
    const nextPoint = route[currentIndex + 1]
    const segmentDistance = calculateDistance(currentPoint, nextPoint)
    
    if (totalDistance + segmentDistance >= stepSize) {
      // We need to interpolate between current and next point
      const remainingDistance = stepSize - totalDistance
      const ratio = remainingDistance / segmentDistance
      
      const nextLocation = {
        lat: currentPoint.lat + (nextPoint.lat - currentPoint.lat) * ratio,
        lng: currentPoint.lng + (nextPoint.lng - currentPoint.lng) * ratio
      }
      
      console.log(`Interpolated next point: ${nextLocation.lat.toFixed(6)}, ${nextLocation.lng.toFixed(6)}`)
      return nextLocation
    }
    
    totalDistance += segmentDistance
    currentIndex++
  }

  // If we've reached the end of the route, return the last point
  console.log('Reached end of route, returning last point')
  return route[route.length - 1]
}
